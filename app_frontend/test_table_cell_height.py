#!/usr/bin/env python3
"""
测试表格单元格高度调整
"""

from datetime import datetime
from xhtml2pdf import pisa
from io import BytesIO

def format_number_for_pdf(value):
    """为PDF格式化数值，使用HTML上标"""
    if value is None:
        return 'Not provided'
    
    try:
        num = float(value)
        
        # 处理零值
        if num == 0:
            return '0'
        
        # 处理整数
        if num == int(num) and abs(num) < 1000:
            return str(int(num))
        
        abs_num = abs(num)
        
        # 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            # 使用标准的科学记数法格式，保留3位有效数字
            scientific_str = f"{num:.2e}"  # 保留2位小数
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)
            
            # 格式化为标准的10为底科学记数法，使用HTML上标
            return f"{mantissa} × 10<sup>{exponent}</sup>"
        
        # 对于正常范围的数值，保留3位有效数字
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)

def format_relationship(value):
    """格式化关系字段"""
    if not value:
        return 'Not provided'
    relationship_map = {
        'my_self': 'Myself',
        'my_father': 'My Father',
        'my_mother': 'My Mother',
        'my_father_in_law': 'My Father in Law',
        'my_mother_in_law': 'My Mother in Law',
        'my_grandfather': 'My Grandfather',
        'my_grandmother': 'My Grandmother',
        'my_friend': 'My Friend'
    }
    return relationship_map.get(value, value)

def format_occupation(value):
    """格式化职业字段"""
    if not value:
        return 'Not provided'
    occupation_map = {
        'student': 'Student',
        'retired': 'Retired',
        'unemployed': 'Unemployed'
    }
    return occupation_map.get(value, value.replace('_', ' ').title())

def test_table_cell_height():
    """测试表格单元格高度"""
    
    # 测试数据
    test_data = {
        'filename': 'cell_height_test.wav',
        'age': '67',
        'relationship': 'my_self',
        'occupation': 'retired'
    }
    
    test_result = {
        'Predicted mmse score': 26.1,
        'Percentile': 84,
        'Model': 'Cell Height Test Model',
        'Model performance': {
            'RMSE': 1.8,
            'Pearson correlation coefficient': 0.92
        },
        'Transcribed with color': '<span class="word-normal">This is a test transcription for cell height optimization.</span>',
        'Selected features': {
            'lexicosyntactic': [
                {
                    'feature name': 'word_count',
                    'value': 195,
                    'clsi lower': 150,
                    'clsi upper': 280,
                    'brief introduction': 'Total number of words spoken during the cognitive assessment task'
                },
                {
                    'feature name': 'sentence_complexity',
                    'value': 0.00089,
                    'clsi lower': 0.0003,
                    'clsi upper': 0.003,
                    'brief introduction': 'Measure of syntactic complexity in sentence structure and grammar'
                }
            ],
            'is10': [
                {
                    'feature name': 'f0_mean',
                    'value': 188.7,
                    'clsi lower': 160,
                    'clsi upper': 240,
                    'brief introduction': 'Mean fundamental frequency (pitch) of the voice during speech'
                },
                {
                    'feature name': 'large_value_test',
                    'value': 2340,
                    'clsi lower': 1500,
                    'clsi upper': 3000,
                    'brief introduction': 'Test feature with large numerical value for formatting verification'
                }
            ]
        }
    }
    
    # 处理 Model performance 数据
    model_performance = test_result.get('Model performance', {})
    if isinstance(model_performance, dict):
        rmse_value = format_number_for_pdf(model_performance.get('RMSE', 'N/A'))
        pearson_value = format_number_for_pdf(model_performance.get('Pearson correlation coefficient', 'N/A'))
    else:
        rmse_value = format_number_for_pdf(model_performance)
        pearson_value = 'N/A'
    
    # 获取转录文本
    transcribed_with_color = test_result.get('Transcribed with color', '')
    transcribed = test_result.get('Transcribed', 'Transcription not available')
    
    # 使用彩色转录文本，如果没有则使用普通转录
    if transcribed_with_color and transcribed_with_color.strip():
        transcription_content = transcribed_with_color
    else:
        transcription_content = f'<span class="word-normal">{transcribed}</span>'
    
    # 创建HTML模板 - 重点测试单元格高度优化
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Table Cell Height Test</title>
        <style>
            @page {{
                size: A4;
                margin: 1.5cm;
            }}
            
            body {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #EAEAEA;
                margin: 0;
                padding: 2rem;
                color: #000000;
            }}
            
            .report-page {{
                max-width: 900px;
                margin: 0 auto;
                background: #FFFFFF;
                border: 1px solid #000000;
            }}
            
            .report-header {{
                padding: 2rem;
                border-bottom: 2px solid #000000;
            }}
            
            .header-main {{
                text-align: center;
                margin-bottom: 2rem;
            }}
            
            .header-main h1 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.8rem;
                margin: 0 0 0.25rem 0;
                font-weight: bold;
            }}
            
            .header-main p {{
                font-size: 1rem;
                margin: 0;
                font-weight: bold;
                color: #333;
            }}
            
            /* Demographics Table: padding 0.35rem 0.5rem (保持不变) */
            .demographics-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                border: 1px solid #000;
            }}
            
            .demographics-table td {{
                padding: 0.35rem 0.5rem;  /* 保持紧凑 */
                border: 1px solid #CCC;
            }}
            
            .demographics-table .label {{
                font-weight: bold;
                white-space: nowrap;
                color: #000;
            }}
            
            .demographics-table .value {{
                color: #000;
            }}
            
            .report-body {{
                padding: 2rem;
                font-family: 'Times New Roman', Times, serif;
            }}
            
            .report-section {{
                margin-bottom: 3.5rem;
                page-break-inside: avoid;
            }}
            
            .section-title {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.6rem;
                font-weight: bold;
                color: #000;
                border-bottom: 2px solid #000;
                padding-bottom: 0.8rem;
                margin: 0 0 1.5rem 0;
                padding-top: 1rem;
            }}
            
            /* Results Table: padding 从 0.75rem 调整为 0.5rem */
            .results-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                margin-bottom: 2.5rem;
                border: 2px solid black;
            }}
            
            .results-table th, .results-table td {{
                border: 1px solid black;
                padding: 0.5rem;  /* 从 0.75rem 降低到 0.5rem */
                text-align: left;
            }}
            
            .results-table th {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #F0F0F0;
                font-weight: bold;
                font-size: 1.2rem;
            }}
            
            .results-table .result-value {{
                font-weight: bold;
                text-align: center;
                font-size: 1.2rem;
            }}
            
            .transcription-box {{
                background: #F8F8F8;
                border: 2px solid #CCC;
                border-left: 6px solid #000000;
                padding: 1.5rem 2rem;
                margin-top: 1.5rem;
                margin-bottom: 2rem;
            }}
            
            .transcription-box h4 {{
                font-family: 'Times New Roman', Times, serif;
                font-weight: bold;
                font-size: 1.3rem;
                margin-bottom: 1rem;
            }}
            
            .transcription-content {{
                margin: 0;
                line-height: 1.8;
                font-style: italic;
                color: #000;
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.1rem;
            }}
            
            .features-grid {{
                margin-top: 2rem;
            }}
            
            .feature-category {{
                margin-bottom: 3rem;
                page-break-inside: avoid;
            }}
            
            .feature-category h4 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.4rem;
                font-weight: bold;
                margin: 0 0 1.5rem 0;
                color: #000;
                border-bottom: 1px solid #CCC;
                padding-bottom: 0.5rem;
            }}
            
            /* Feature Table: padding 从 0.5rem 调整为 0.4rem */
            .feature-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                margin-bottom: 2rem;
                border: 1px solid #000;
            }}
            
            .feature-table th, .feature-table td {{
                border: 1px solid #E0E0E0;
                padding: 0.4rem;  /* 从 0.5rem 降低到 0.4rem */
                text-align: left;
            }}
            
            .feature-table th {{
                background-color: #F8F8F8;
                font-weight: bold;
                font-size: 1.2rem;
            }}
            
            .feature-table td:nth-child(2), .feature-table th:nth-child(2) {{
                text-align: center;
                width: 20%;
                position: relative;
            }}
            
            .feature-table td:nth-child(3), .feature-table th:nth-child(3) {{
                text-align: center;
                width: 25%;
            }}
            
            /* 单词上色样式 */
            .transcription-content .word-normal {{
                color: #333;
            }}
        </style>
    </head>
    <body>
        <div class="report-page">
            <header class="report-header">
                <div class="header-main">
                    <h1>Table Cell Height Optimization Test</h1>
                    <p>Testing Reduced Cell Padding for Compact Layout</p>
                </div>
                
                <!-- Demographics Table: padding 0.35rem 0.5rem -->
                <table class="demographics-table">
                    <tbody>
                        <tr>
                            <td class="label">Audio File Name:</td>
                            <td class="value">{test_data.get('filename', 'N/A')}</td>
                            <td class="label">Date of Report:</td>
                            <td class="value">{datetime.now().strftime('%d-%m-%Y')}</td>
                        </tr>
                        <tr>
                            <td class="label">This Audio is Spoken by:</td>
                            <td class="value">{format_relationship(test_data.get('relationship'))}</td>
                            <td class="label">Occupation:</td>
                            <td class="value">{format_occupation(test_data.get('occupation'))}</td>
                        </tr>
                        <tr>
                            <td class="label">Age:</td>
                            <td class="value" colspan="3">{test_data.get('age', 'N/A')}</td>
                        </tr>
                    </tbody>
                </table>
            </header>

            <main class="report-body">
                <!-- Results Table: padding 0.5rem -->
                <section class="report-section">
                    <h3 class="section-title">Prediction Results</h3>
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th style="width: 50%; text-align: center;">Predicted MMSE Score</th>
                                <th style="width: 50%; text-align: center;">Percentile</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="result-value">{format_number_for_pdf(test_result.get('Predicted mmse score', 'N/A'))}</td>
                                <td class="result-value">{test_result.get('Percentile', 'N/A')}%</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="report-section">
                    <h3 class="section-title">Technical Details</h3>
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th style="width: 50%; text-align: center;">Model</th>
                                <th style="width: 50%; text-align: center;">Model Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="result-value">{test_result.get('Model', 'N/A')}</td>
                                <td>
                                    <div style="margin-bottom: 10px;">
                                        <strong>RMSE:</strong> {rmse_value}
                                    </div>
                                    <div>
                                        <strong>Pearson correlation coefficient:</strong> {pearson_value}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="report-section">
                    <h3 class="section-title">Speech Analysis</h3>
                    <div class="transcription-box">
                        <h4>Enhanced Transcription with Linguistic Analysis</h4>
                        <div class="transcription-content">
                            {transcription_content}
                        </div>
                    </div>
                </section>
    """
    
    # 添加特征分析部分
    features = test_result.get('Selected features', {})
    if features:
        html_template += """
                <section class="report-section">
                    <h3 class="section-title">Feature Analysis</h3>
                    <div class="features-grid">"""
        
        # 处理特征类别
        category_keys = list(features.keys())
        if 'is10' in category_keys:
            category_keys.remove('is10')
            category_keys.append('is10')
        
        for category in category_keys:
            feature_list = features[category]
            if not isinstance(feature_list, list) or len(feature_list) == 0:
                continue
            
            # 映射类别名称
            category_name = category.replace('_', ' ')
            if category_name == 'digipsych prosody feats':
                category_name = 'Prosodic'
            elif category_name == 'lexicosyntactic':
                category_name = 'Linguistic'
            elif category_name == 'is10':
                category_name = 'Acoustic'
            
            html_template += f"""
                        <div class="feature-category">
                            <h4>{category_name} Features</h4>
                            <!-- Feature Table: padding 0.4rem -->
                            <table class="feature-table">
                                <thead>
                                    <tr>
                                        <th>Feature</th>
                                        <th>Value</th>
                                        <th>Reference Range</th>
                                    </tr>
                                </thead>
                                <tbody>"""
            
            for feature_obj in feature_list:
                name = feature_obj.get('feature name', 'N/A')
                value = feature_obj.get('value', 'N/A')
                lower = feature_obj.get('clsi lower')
                upper = feature_obj.get('clsi upper')
                brief_intro = feature_obj.get('brief introduction', '')
                
                display_name = name.replace('_', ' ')
                
                # 检查是否异常
                indicator = ''
                if lower is not None and upper is not None and value != 'N/A':
                    try:
                        num_value = float(value)
                        num_lower = float(lower)
                        num_upper = float(upper)
                        if num_value > num_upper:
                            indicator = ' ▲'
                        elif num_value < num_lower:
                            indicator = ' ▼'
                    except (ValueError, TypeError):
                        pass
                
                # 格式化参考范围
                if lower is not None and upper is not None:
                    ref_range = f"{format_number_for_pdf(lower)} - {format_number_for_pdf(upper)}"
                else:
                    ref_range = 'Not provided'
                
                # 特征名称处理 - 包含简介
                feature_name_display = display_name
                if brief_intro:
                    feature_name_display += f"<br><small style='color: #666666; font-style: italic; font-size: 0.9em;'>{brief_intro}</small>"
                
                html_template += f"""
                                    <tr>
                                        <td>{feature_name_display}</td>
                                        <td style="text-align: center;"><strong>{format_number_for_pdf(value)}</strong>{indicator}</td>
                                        <td style="text-align: center;">{ref_range}</td>
                                    </tr>"""
            
            html_template += """
                                </tbody>
                            </table>
                        </div>"""
        
        html_template += """
                    </div>
                </section>"""
    
    # 添加页脚和总结
    html_template += f"""
            </main>
        </div>
        
        <!-- 单元格高度调整总结 -->
        <div style="margin-top: 2rem; padding: 1rem; background: #f0f0f0; border: 1px solid #ccc;">
            <h3 style="margin: 0 0 1rem 0; color: #333;">单元格高度优化总结：</h3>
            <ul style="margin: 0; padding-left: 1.5rem; color: #666;">
                <li><strong>Demographics Table：</strong> padding: 0.35rem 0.5rem (保持不变，已经紧凑)</li>
                <li><strong>Results Table：</strong> padding: 从 0.75rem 降低到 0.5rem</li>
                <li><strong>Feature Table：</strong> padding: 从 0.5rem 降低到 0.4rem</li>
                <li><strong>优化效果：</strong></li>
                <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                    <li>减少了表格的垂直空间占用</li>
                    <li>保持了内容的可读性</li>
                    <li>使PDF更加紧凑和专业</li>
                    <li>足够显示单元格中的所有内容</li>
                </ul>
            </ul>
        </div>
    </body>
    </html>"""
    
    # 生成PDF
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html_template.encode("UTF-8")), result)
    
    if not pdf.err:
        return result.getvalue()
    else:
        raise Exception(f"PDF generation failed: {pdf.err}")

if __name__ == '__main__':
    try:
        pdf_bytes = test_table_cell_height()
        
        # 保存PDF文件
        output_filename = 'test_table_cell_height.pdf'
        with open(output_filename, 'wb') as f:
            f.write(pdf_bytes)
        
        print("✅ 表格单元格高度优化测试成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print(f"📁 文件保存为: {output_filename}")
        print("\n🔧 单元格高度调整:")
        print("- Demographics Table: padding 0.35rem 0.5rem (保持不变)")
        print("- Results Table: padding 从 0.75rem 降低到 0.5rem")
        print("- Feature Table: padding 从 0.5rem 降低到 0.4rem")
        print("\n📏 优化效果:")
        print("- 减少了表格的垂直空间占用")
        print("- 保持了内容的可读性和专业外观")
        print("- 使PDF布局更加紧凑")
        print("- 足够显示单元格中的所有内容")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
