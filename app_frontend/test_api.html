<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面 - 认知健康</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }

        .test-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            font-weight: 600;
        }

        .test-body {
            padding: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: black;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .token-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 认知健康 API 测试页面</h1>
            <p>测试用户认证和音频分析API接口</p>
        </div>

        <div class="content">
            <div class="token-info">
                <strong>当前状态:</strong>
                <div id="authStatus">未登录</div>
                <button class="btn btn-warning" onclick="clearTokens()">清除Token</button>
                <button class="btn btn-primary" onclick="checkAuthStatus()">检查状态</button>
            </div>

            <div class="grid">
                <!-- 用户注册 -->
                <div class="test-section">
                    <div class="test-header">用户注册</div>
                    <div class="test-body">
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="registerEmail" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>密码:</label>
                            <input type="password" id="registerPassword" value="testpassword123">
                        </div>
                        <div class="form-group">
                            <label>确认密码:</label>
                            <input type="password" id="registerPasswordConfirm" value="testpassword123">
                        </div>
                        <div class="form-group">
                            <label>名字:</label>
                            <input type="text" id="registerFirstName" value="测试">
                        </div>
                        <div class="form-group">
                            <label>姓氏:</label>
                            <input type="text" id="registerLastName" value="用户">
                        </div>
                        <button class="btn btn-primary" onclick="testRegister()">注册</button>
                        <div id="registerResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 用户登录 -->
                <div class="test-section">
                    <div class="test-header">用户登录</div>
                    <div class="test-body">
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="loginEmail" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>密码:</label>
                            <input type="password" id="loginPassword" value="testpassword123">
                        </div>
                        <button class="btn btn-success" onclick="testLogin()">登录</button>
                        <div id="loginResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 邮箱检查 -->
                <div class="test-section">
                    <div class="test-header">邮箱检查</div>
                    <div class="test-body">
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="checkEmail" value="<EMAIL>">
                        </div>
                        <button class="btn btn-primary" onclick="testCheckEmail()">检查邮箱</button>
                        <div id="checkEmailResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 重新发送验证邮件 -->
                <div class="test-section">
                    <div class="test-header">重新发送验证邮件</div>
                    <div class="test-body">
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="resendEmail" value="<EMAIL>">
                        </div>
                        <button class="btn btn-warning" onclick="testResendVerification()">重新发送</button>
                        <div id="resendResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 密码重置 -->
                <div class="test-section">
                    <div class="test-header">密码重置</div>
                    <div class="test-body">
                        <div class="form-group">
                            <label>邮箱:</label>
                            <input type="email" id="resetEmail" value="<EMAIL>">
                        </div>
                        <button class="btn btn-danger" onclick="testPasswordReset()">请求重置</button>
                        <div id="resetResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 获取用户资料 -->
                <div class="test-section">
                    <div class="test-header">获取用户资料</div>
                    <div class="test-body">
                        <button class="btn btn-primary" onclick="testGetProfile()">获取资料</button>
                        <div id="profileResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 健康检查 -->
                <div class="test-section">
                    <div class="test-header">健康检查</div>
                    <div class="test-body">
                        <button class="btn btn-success" onclick="testHealthCheck()">健康检查</button>
                        <div id="healthResult" class="result" style="display: none;"></div>
                    </div>
                </div>

                <!-- 用户登出 -->
                <div class="test-section">
                    <div class="test-header">用户登出</div>
                    <div class="test-body">
                        <button class="btn btn-danger" onclick="testLogout()">登出</button>
                        <div id="logoutResult" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/audio_upload/static/js/auth_api.js"></script>
    <script>
        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 检查认证状态
        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const user = localStorage.getItem('user_info');
            const status = document.getElementById('authStatus');
            
            if (token && user) {
                const userInfo = JSON.parse(user);
                status.innerHTML = `已登录: ${userInfo.email}`;
            } else {
                status.innerHTML = '未登录';
            }
        }

        // 清除Token
        function clearTokens() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            checkAuthStatus();
        }

        // 测试注册
        async function testRegister() {
            try {
                const result = await authAPI.register({
                    email: document.getElementById('registerEmail').value,
                    password: document.getElementById('registerPassword').value,
                    passwordConfirm: document.getElementById('registerPasswordConfirm').value,
                    firstName: document.getElementById('registerFirstName').value,
                    lastName: document.getElementById('registerLastName').value
                });
                showResult('registerResult', result);
            } catch (error) {
                showResult('registerResult', { error: error.message }, true);
            }
        }

        // 测试登录
        async function testLogin() {
            try {
                const result = await authAPI.login(
                    document.getElementById('loginEmail').value,
                    document.getElementById('loginPassword').value
                );
                showResult('loginResult', result);
                checkAuthStatus();
            } catch (error) {
                showResult('loginResult', { error: error.message }, true);
            }
        }

        // 测试邮箱检查
        async function testCheckEmail() {
            try {
                const result = await authAPI.checkEmail(document.getElementById('checkEmail').value);
                showResult('checkEmailResult', result);
            } catch (error) {
                showResult('checkEmailResult', { error: error.message }, true);
            }
        }

        // 测试重新发送验证邮件
        async function testResendVerification() {
            try {
                const result = await authAPI.resendVerification(document.getElementById('resendEmail').value);
                showResult('resendResult', result);
            } catch (error) {
                showResult('resendResult', { error: error.message }, true);
            }
        }

        // 测试密码重置
        async function testPasswordReset() {
            try {
                const result = await authAPI.requestPasswordReset(document.getElementById('resetEmail').value);
                showResult('resetResult', result);
            } catch (error) {
                showResult('resetResult', { error: error.message }, true);
            }
        }

        // 测试获取用户资料
        async function testGetProfile() {
            try {
                const result = await authAPI.getUserProfile();
                showResult('profileResult', result);
            } catch (error) {
                showResult('profileResult', { error: error.message }, true);
            }
        }

        // 测试健康检查
        async function testHealthCheck() {
            try {
                const result = await authAPI.healthCheck();
                showResult('healthResult', result);
            } catch (error) {
                showResult('healthResult', { error: error.message }, true);
            }
        }

        // 测试登出
        async function testLogout() {
            try {
                const result = await authAPI.logout();
                showResult('logoutResult', result);
                checkAuthStatus();
            } catch (error) {
                showResult('logoutResult', { error: error.message }, true);
            }
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();
        });
    </script>
</body>
</html>
