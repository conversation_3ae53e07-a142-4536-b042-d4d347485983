{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Audio Analysis History - MedVoice Pro</title>

    <!-- Performance Optimizations -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>

    <!-- Critical CSS Inline -->
    <style>
        /* Modern CSS Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Custom Properties */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1e40af;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Navigation */
        .back-btn {
            position: fixed;
            top: 1.5rem;
            left: 1.5rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 0.75rem 1.25rem;
            color: var(--primary-color);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
            color: var(--primary-color);
            text-decoration: none;
        }

        /* Header Section */
        .header {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            line-height: 1.2;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.125rem;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        /* Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: var(--background-color);
            padding: 0.375rem;
            border-radius: var(--radius-lg);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Filter Section */
        .filter-section {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.75rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .search-container {
            display: flex;
            gap: 0.25rem;
            align-items: stretch;
        }

        .search-container .form-input {
            flex: 1;
        }

        .search-container .clear-btn {
            flex-shrink: 0;
            white-space: nowrap;
        }

        .form-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-input, .form-select {
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .clear-btn {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 0.875rem 0.75rem;
            border-radius: var(--radius-lg);
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.25rem;
            height: 100%;
            min-width: fit-content;
        }

        .clear-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* History Items */
        .history-section {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        .history-grid {
            display: grid;
            gap: 1rem;
        }

        .history-item {
            background: var(--background-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            border-radius: var(--radius-sm) 0 0 var(--radius-sm);
            transform: scaleY(0);
            transition: var(--transition);
        }

        .history-item:hover {
            background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
            transform: translateX(4px);
            box-shadow: var(--shadow-lg);
            color: #1e293b !important;
            border-left: 4px solid var(--primary-color);
        }

        .history-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .view-details-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .view-details-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .view-details-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .history-item:hover::before {
            transform: scaleY(1);
        }

        .history-item:hover .history-title,
        .history-item:hover .history-filename,
        .history-item:hover .history-details,
        .history-item:hover .history-date {
            color: #1e293b !important;
        }

        .history-item:hover .history-details .detail-item {
            color: #475569 !important;
        }

        .history-item:hover .history-filename span {
            color: #1e293b !important;
        }

        .history-item:hover .history-status {
            background: rgba(255, 255, 255, 0.9) !important;
            color: inherit !important;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .history-filename {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .history-date {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .history-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .history-details {
            display: flex;
            gap: 1rem;
            margin: 0.5rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .history-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-processing {
            background: #fef3c7;
            color: #92400e;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Loading and Empty States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .loading i {
            margin-right: 0.75rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--text-muted);
        }

        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .filter-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                padding: 2rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .back-btn {
                top: 1rem;
                left: 1rem;
                padding: 0.5rem 1rem;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0.75rem;
            }

            .header {
                padding: 1.5rem;
            }

            .filter-section,
            .history-section {
                padding: 0.75rem;
            }

            .filter-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Donation Modal Styles */
        .donation-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
        }

        .donation-modal-overlay.show {
            display: flex;
        }

        .donation-modal {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .donation-modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .donation-modal-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .donation-modal-content {
            padding: 2rem;
        }

        .donation-description {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .donation-amounts {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-bottom: 2rem;
        }

        .donation-amount {
            background: var(--background-color);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
        }

        .donation-amount:hover {
            border-color: var(--primary-color);
            background: rgba(59, 130, 246, 0.05);
        }

        .donation-amount.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .donation-amount.decline {
            grid-column: 1 / -1;
            background: var(--error-color);
            color: white;
            border-color: var(--error-color);
        }

        .donation-amount.decline:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        .donation-modal-footer {
            background: var(--background-color);
            padding: 1.5rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .donation-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .donation-btn-cancel {
            background: var(--secondary-color);
            color: white;
        }

        .donation-btn-cancel:hover {
            background: #475569;
        }

        .donation-btn-proceed {
            background: var(--success-color);
            color: white;
        }

        .donation-btn-proceed:hover {
            background: #059669;
        }

        .donation-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Payment Modal Styles */
        .payment-modal {
            max-width: 600px;
        }

        .payment-form {
            margin-top: 1rem;
        }

        .payment-element {
            margin-bottom: 1rem;
        }

        .payment-loading {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .payment-loading i {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Responsive Design for Donation Modal */
        @media (max-width: 768px) {
            .donation-amounts {
                grid-template-columns: 1fr;
            }

            .donation-modal-footer {
                flex-direction: column;
            }
        }
    </style>

    <!-- Stripe -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- Application Configuration -->
    <script>
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
    </script>
</head>
<body>
    <!-- Navigation -->
    <a href="/audio_upload/" class="back-btn" aria-label="Back to upload">
        <i class="fas fa-arrow-left" aria-hidden="true"></i>
        <span>Back to Upload</span>
    </a>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Speech Analysis History</h1>
            <p>View cognitive assessment records</p>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <span id="total-count" class="stat-value">0</span>
                    <span class="stat-label">Total Records</span>
                </div>
                <div class="stat-card">
                    <span id="completed-count" class="stat-value">0</span>
                    <span class="stat-label">Completed</span>
                </div>
                <div class="stat-card">
                    <span id="processing-count" class="stat-value">0</span>
                    <span class="stat-label">Processing</span>
                </div>
                <div class="stat-card">
                    <span id="failed-count" class="stat-value">0</span>
                    <span class="stat-label">Failed</span>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-grid">
                <div class="form-group">
                    <label class="form-label" for="statusFilter">Status</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">All Status</option>
                        <option value="completed">Completed</option>
                        <option value="processing">Processing</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="relationshipFilter">Spoken by</label>
                    <select id="relationshipFilter" class="form-select">
                        <option value="">All Speakers</option>
                        <option value="my_self">Myself</option>
                        <option value="my_father">My Father</option>
                        <option value="my_mother">My Mother</option>
                        <option value="my_father_in_law">My Father in law</option>
                        <option value="my_mother_in_law">My Mother in law</option>
                        <option value="my_grandfather">My Grandfather</option>
                        <option value="my_grandmother">My Grandmother</option>
                        <option value="great_grandfather">My Great-Grandfather</option>
                        <option value="great_grandmother">My Great-Grandmother</option>
                        <option value="my_friend">My Friend</option>
                        <option value="others">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="dateFrom">From Date</label>
                    <input type="date" id="dateFrom" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label" for="dateTo">To Date</label>
                    <input type="date" id="dateTo" class="form-input">
                </div>
                <div class="form-group search-group">
                    <label class="form-label" for="searchFilter">Search</label>
                    <div class="search-container">
                        <input type="text" id="searchFilter" class="form-input" placeholder="Search filename...">
                        <button id="clearFilters" class="clear-btn">
                            <i class="fas fa-times"></i>
                            Clear All
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div class="history-section">
            <div id="historyList" class="history-grid">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <span>Loading analysis history...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Donation Modal -->
    <div class="donation-modal-overlay" id="donation-modal-overlay">
        <div class="donation-modal">
            <div class="donation-modal-header">
                <h3><i class="fas fa-heart"></i> Support Our Research</h3>
            </div>

            <div class="donation-modal-content">
                <div class="donation-description">
                    <p>Your contribution helps us continue developing advanced AI tools for cognitive health assessment. Thank you for supporting our research!</p>
                </div>

                <div class="donation-amounts">
                    <div class="donation-amount" data-amount="50">$50</div>
                    <div class="donation-amount" data-amount="100">$100</div>
                    <div class="donation-amount selected" data-amount="150">$150</div>
                    <div class="donation-amount" data-amount="200">$200</div>
                    <div class="donation-amount" data-amount="250">$250</div>
                    <div class="donation-amount" data-amount="300">$300</div>
                    <div class="donation-amount" data-amount="350">$350</div>
                    <div class="donation-amount" data-amount="400">$400</div>
                    <div class="donation-amount" data-amount="450">$450</div>
                    <div class="donation-amount" data-amount="500">$500</div>
                    <div class="donation-amount decline" data-amount="0">I prefer not to donate</div>
                </div>
            </div>

            <div class="donation-modal-footer">
                <button type="button" class="donation-btn donation-btn-cancel" id="donation-cancel-btn">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="donation-btn donation-btn-proceed" id="donation-proceed-btn">
                    <i class="fas fa-credit-card"></i> Proceed to Payment
                </button>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="donation-modal-overlay" id="payment-modal-overlay">
        <div class="donation-modal payment-modal">
            <div class="donation-modal-header">
                <h3><i class="fas fa-credit-card"></i> Complete Payment</h3>
            </div>

            <div class="donation-modal-content">
                <div id="payment-loading" class="payment-loading">
                    <i class="fas fa-spinner"></i>
                    <p>Setting up secure payment...</p>
                </div>

                <div id="payment-form" class="payment-form" style="display: none;">
                    <div id="payment-element" class="payment-element">
                        <!-- Stripe Elements will be inserted here -->
                    </div>
                </div>
            </div>

            <div class="donation-modal-footer">
                <button type="button" class="donation-btn donation-btn-cancel" id="payment-cancel-btn">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="donation-btn donation-btn-proceed" id="payment-submit-btn" disabled>
                    <i class="fas fa-lock"></i> Complete Donation
                </button>
            </div>
        </div>
    </div>

    <!-- High-Performance History Application -->
    <script>
        /**
         * Fast History Application - Based on Profile.html Architecture
         * Optimized for speed and user experience
         */
        class FastHistoryApp {
            constructor() {
                this.config = {
                    apiBaseUrl: window.API_BASE_URL,
                    localBaseUrl: window.LOCAL_BASE_URL
                };

                this.cache = new Map();
                this.isLoading = false;
                this.data = {
                    all: [],
                    filtered: []
                };

                // Cache DOM elements
                this.dom = {
                    historyList: document.getElementById('historyList'),
                    totalCount: document.getElementById('total-count'),
                    completedCount: document.getElementById('completed-count'),
                    processingCount: document.getElementById('processing-count'),
                    failedCount: document.getElementById('failed-count'),
                    statusFilter: document.getElementById('statusFilter'),
                    relationshipFilter: document.getElementById('relationshipFilter'),
                    searchFilter: document.getElementById('searchFilter'),
                    dateFrom: document.getElementById('dateFrom'),
                    dateTo: document.getElementById('dateTo'),
                    clearFilters: document.getElementById('clearFilters')
                };

                // Debounced filter function
                this.debouncedFilter = this.debounce(this.applyFilters.bind(this), 150);

                this.init();
            }

            async init() {
                console.log('Initializing FastHistoryApp...');

                try {
                    this.setupEventListeners();
                    this.setupDonationEventListeners();
                    await this.loadHistoryData();
                } catch (error) {
                    console.error('Failed to initialize history app:', error);
                    this.showError('Failed to load history data');
                }
            }

            // Authentication helpers (using JWT)
            getAuthToken() {
                return localStorage.getItem('access_token');
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp < Date.now() / 1000;
                } catch {
                    return true;
                }
            }

            async makeAuthenticatedRequest(url, options = {}) {
                const token = this.getAuthToken();

                if (!token || this.isTokenExpired(token)) {
                    window.location.href = '/login/';
                    return null;
                }

                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                    return null;
                }

                return response;
            }

            // Load history data
            async loadHistoryData() {
                if (this.isLoading) return;
                this.isLoading = true;

                try {
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (!response) return;

                    if (response.ok) {
                        const result = await response.json();
                        this.data.all = result.data || result.results || [];
                        this.data.filtered = [...this.data.all];

                        this.updateStatistics();
                        this.displayHistory(this.data.filtered);
                        this.cache.set('historyData', this.data.all);

                        console.log(`Loaded ${this.data.all.length} history records`);
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('Failed to load history:', error);
                    this.showError('Failed to load analysis history');
                } finally {
                    this.isLoading = false;
                }
            }

            // Setup event listeners
            setupEventListeners() {
                // Filter event listeners
                if (this.dom.statusFilter) {
                    this.dom.statusFilter.addEventListener('change', this.debouncedFilter);
                }
                if (this.dom.relationshipFilter) {
                    this.dom.relationshipFilter.addEventListener('change', this.debouncedFilter);
                }
                if (this.dom.searchFilter) {
                    this.dom.searchFilter.addEventListener('input', this.debouncedFilter);
                }
                if (this.dom.dateFrom) {
                    this.dom.dateFrom.addEventListener('change', this.debouncedFilter);
                }
                if (this.dom.dateTo) {
                    this.dom.dateTo.addEventListener('change', this.debouncedFilter);
                }
                if (this.dom.clearFilters) {
                    this.dom.clearFilters.addEventListener('click', () => this.clearFilters());
                }
            }

            // Apply filters
            applyFilters() {
                const filters = {
                    status: this.dom.statusFilter?.value || '',
                    relationship: this.dom.relationshipFilter?.value || '',
                    search: this.dom.searchFilter?.value.toLowerCase() || '',
                    dateFrom: this.dom.dateFrom?.value || '',
                    dateTo: this.dom.dateTo?.value || ''
                };

                this.data.filtered = this.data.all.filter(item => {
                    // Status filter
                    if (filters.status && item.status !== filters.status) return false;

                    // Relationship filter
                    if (filters.relationship && item.relationship !== filters.relationship) return false;

                    // Search filter
                    if (filters.search) {
                        const filename = (item.filename || '').toLowerCase();
                        if (!filename.includes(filters.search)) return false;
                    }

                    // Date filters
                    if (filters.dateFrom) {
                        const itemDate = new Date(item.upload_time);
                        const fromDate = new Date(filters.dateFrom);
                        if (itemDate < fromDate) return false;
                    }

                    if (filters.dateTo) {
                        const itemDate = new Date(item.upload_time);
                        const toDate = new Date(filters.dateTo);
                        toDate.setHours(23, 59, 59, 999);
                        if (itemDate > toDate) return false;
                    }

                    return true;
                });

                this.updateStatistics();
                this.displayHistory(this.data.filtered);
            }

            // Clear all filters
            clearFilters() {
                // Clear all filter inputs
                Object.values(this.dom).forEach(element => {
                    if (element && (element.tagName === 'INPUT' || element.tagName === 'SELECT')) {
                        element.value = '';
                    }
                });

                this.data.filtered = [...this.data.all];
                this.updateStatistics();
                this.displayHistory(this.data.filtered);
            }

            // Update statistics
            updateStatistics() {
                const stats = this.calculateStatistics(this.data.all);
                const filteredStats = this.calculateStatistics(this.data.filtered);

                if (this.dom.totalCount) this.dom.totalCount.textContent = this.data.all.length;
                if (this.dom.completedCount) this.dom.completedCount.textContent = stats.completed;
                if (this.dom.processingCount) this.dom.processingCount.textContent = stats.processing;
                if (this.dom.failedCount) this.dom.failedCount.textContent = stats.failed;
            }

            // Calculate statistics
            calculateStatistics(data) {
                return {
                    total: data.length,
                    completed: data.filter(item => item.status === 'completed').length,
                    processing: data.filter(item => item.status === 'processing').length,
                    failed: data.filter(item => item.status === 'failed').length
                };
            }

            // Display history items
            displayHistory(items) {
                if (!this.dom.historyList) return;

                if (items.length === 0) {
                    this.dom.historyList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-microphone-slash"></i>
                            <h3>No Analysis Records</h3>
                            <p>No records match your current filters.</p>
                        </div>
                    `;
                    return;
                }

                // Use document fragment for better performance
                const fragment = document.createDocumentFragment();

                items.forEach(item => {
                    const historyItem = this.createHistoryItem(item);
                    fragment.appendChild(historyItem);
                });

                this.dom.historyList.innerHTML = '';
                this.dom.historyList.appendChild(fragment);
            }

            // Create history item element
            createHistoryItem(item) {
                const div = document.createElement('div');
                div.className = 'history-item';

                const statusClass = this.getStatusClass(item.status);
                const mmseScore = this.parseMMSEScore(item.result);
                const relationship = this.formatRelationship(item.relationship);
                const formattedDate = this.formatDate(item.upload_time);

                div.innerHTML = `
                    <div class="history-header">
                        <div class="history-filename">
                            <i class="fas fa-file-audio"></i>
                            <span>${this.escapeHtml(item.filename || 'Audio Analysis')}</span>
                        </div>
                        <div class="history-date">${formattedDate}</div>
                    </div>
                    <div class="history-title">MMSE Score: ${mmseScore}</div>
                    <div class="history-details">
                        <span class="detail-item">Spoken by: ${relationship}</span>
                        <span class="detail-item">Age: ${item.age || 'N/A'}</span>
                    </div>
                    <span class="history-status ${statusClass}">${this.getStatusText(item.status)}</span>
                    <div class="history-actions">
                        <button class="view-details-btn" onclick="fastHistoryApp.viewAnalysisDetail('${item.id}')" aria-label="View analysis details">
                            <i class="fas fa-eye"></i>
                            View Details
                        </button>
                    </div>
                `;

                return div;
            }

            // Navigation methods
            viewAnalysisDetail(analysisId) {
                try {
                    // Find the analysis item from cached data
                    const analysisItem = this.data.all.find(item => item.id == analysisId);

                    if (analysisItem) {
                        console.log('Found analysis item:', analysisItem);
                        // Store analysis details for donation process
                        this.currentAnalysisId = analysisId;
                        sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));

                        // Show donation modal first
                        this.showDonationModal();
                    } else {
                        console.error('Analysis item not found');
                        alert('Analysis record not found.');
                    }
                } catch (error) {
                    console.error('Failed to navigate to details:', error);
                    alert('Failed to load analysis details. Please try again.');
                }
            }

            // Donation Modal Methods
            showDonationModal() {
                const modal = document.getElementById('donation-modal-overlay');
                if (modal) {
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }

            hideDonationModal() {
                const modal = document.getElementById('donation-modal-overlay');
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }

            showPaymentModal() {
                const donationModal = document.getElementById('donation-modal-overlay');
                const paymentModal = document.getElementById('payment-modal-overlay');

                if (donationModal) donationModal.classList.remove('show');
                if (paymentModal) {
                    paymentModal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }

            hidePaymentModal() {
                const modal = document.getElementById('payment-modal-overlay');
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }

            proceedToDetails() {
                this.hideDonationModal();
                this.hidePaymentModal();
                window.location.href = '/audio_upload/details/';
            }

            // Setup donation event listeners
            setupDonationEventListeners() {
                // Donation amount selection
                document.querySelectorAll('.donation-amount').forEach(amount => {
                    amount.addEventListener('click', (e) => {
                        document.querySelectorAll('.donation-amount').forEach(a => a.classList.remove('selected'));
                        e.target.classList.add('selected');
                        this.selectedDonationAmount = parseInt(e.target.dataset.amount);
                    });
                });

                // Donation modal buttons
                document.getElementById('donation-cancel-btn')?.addEventListener('click', () => {
                    this.hideDonationModal();
                });

                document.getElementById('donation-proceed-btn')?.addEventListener('click', () => {
                    if (this.selectedDonationAmount === 0) {
                        // User declined to donate, proceed to details
                        this.proceedToDetails();
                    } else {
                        // Proceed to payment
                        this.initializePayment();
                    }
                });

                // Payment modal buttons
                document.getElementById('payment-cancel-btn')?.addEventListener('click', () => {
                    this.hidePaymentModal();
                    this.showDonationModal();
                });

                document.getElementById('payment-submit-btn')?.addEventListener('click', () => {
                    this.submitPayment();
                });

                // Initialize selected amount
                this.selectedDonationAmount = 150; // Default amount
            }

            // Payment Methods
            async initializePayment() {
                try {
                    this.showPaymentModal();

                    // Create donation on backend
                    const response = await fetch(`${window.API_BASE_URL}/api/donations/create/`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.getToken()}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            amount: this.selectedDonationAmount,
                            audio_analysis_id: this.currentAnalysisId
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.currentDonationId = data.data.donation_id;
                        this.setupStripePayment(data.data.client_secret, data.data.publishable_key);
                    } else {
                        throw new Error(data.message || 'Failed to create donation');
                    }
                } catch (error) {
                    console.error('Payment initialization failed:', error);
                    alert('Failed to initialize payment. Please try again.');
                    this.hidePaymentModal();
                    this.showDonationModal();
                }
            }

            setupStripePayment(clientSecret, publishableKey) {
                try {
                    // Initialize Stripe
                    this.stripe = Stripe(publishableKey);

                    // Create payment elements
                    const elements = this.stripe.elements({
                        clientSecret: clientSecret
                    });

                    this.paymentElement = elements.create('payment');

                    // Hide loading and show form
                    document.getElementById('payment-loading').style.display = 'none';
                    document.getElementById('payment-form').style.display = 'block';

                    // Mount payment element
                    this.paymentElement.mount('#payment-element');

                    // Enable submit button
                    document.getElementById('payment-submit-btn').disabled = false;

                } catch (error) {
                    console.error('Stripe setup failed:', error);
                    alert('Failed to setup payment form. Please try again.');
                    this.hidePaymentModal();
                    this.showDonationModal();
                }
            }

            async submitPayment() {
                try {
                    const submitBtn = document.getElementById('payment-submit-btn');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

                    // Confirm payment with Stripe
                    const {error, paymentIntent} = await this.stripe.confirmPayment({
                        elements: this.stripe.elements(),
                        redirect: 'if_required'
                    });

                    if (error) {
                        throw new Error(error.message);
                    }

                    if (paymentIntent.status === 'succeeded') {
                        // Confirm donation on backend
                        const response = await fetch(`${window.API_BASE_URL}/api/donations/confirm/`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${this.getToken()}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                donation_id: this.currentDonationId,
                                payment_intent_id: paymentIntent.id
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            alert('Thank you for your donation! Your contribution helps support our research.');
                            this.proceedToDetails();
                        } else {
                            throw new Error(data.message || 'Failed to confirm donation');
                        }
                    }
                } catch (error) {
                    console.error('Payment failed:', error);
                    alert(`Payment failed: ${error.message}`);

                    // Reset submit button
                    const submitBtn = document.getElementById('payment-submit-btn');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-lock"></i> Complete Donation';
                }
            }

            // Utility methods
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            formatDate(dateStr) {
                try {
                    return new Date(dateStr).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch {
                    return 'Invalid date';
                }
            }

            parseMMSEScore(result) {
                try {
                    if (!result) return 'N/A';
                    const parsed = typeof result === 'string' ? JSON.parse(result) : result;
                    const score = parsed['Predicted mmse score'];
                    return typeof score === 'number' ? score.toFixed(1) : (parseFloat(score) || 0).toFixed(1);
                } catch {
                    return 'N/A';
                }
            }

            formatRelationship(relationship) {
                const relationshipMap = {
                    'my_self': 'Myself',
                    'my_father': 'My Father',
                    'my_mother': 'My Mother',
                    'my_father_in_law': 'My Father in law',
                    'my_mother_in_law': 'My Mother in law',
                    'my_grandfather': 'My Grandfather',
                    'my_grandmother': 'My Grandmother',
                    'great_grandfather': 'My Great-Grandfather',
                    'great_grandmother': 'My Great-Grandmother',
                    'my_friend': 'My Friend',
                    'others': 'Other'
                };
                return relationshipMap[relationship] || relationship || 'Not specified';
            }

            getStatusClass(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'status-completed';
                    case 'processing': return 'status-processing';
                    case 'failed': return 'status-failed';
                    default: return 'status-completed';
                }
            }

            getStatusText(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'Completed';
                    case 'processing': return 'Processing';
                    case 'failed': return 'Failed';
                    default: return 'Unknown';
                }
            }

            showError(message) {
                if (this.dom.historyList) {
                    this.dom.historyList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>Error</h3>
                            <p>${this.escapeHtml(message)}</p>
                        </div>
                    `;
                }
            }
        }

        // Initialize the application
        let fastHistoryApp;
        document.addEventListener('DOMContentLoaded', () => {
            try {
                fastHistoryApp = new FastHistoryApp();
                window.fastHistoryApp = fastHistoryApp; // For debugging
                console.log('Fast History App initialized successfully');
            } catch (error) {
                console.error('Failed to initialize Fast History App:', error);
            }
        });
    </script>
</body>
</html>
