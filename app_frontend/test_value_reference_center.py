#!/usr/bin/env python3
"""
测试Value和Reference Range标签居中显示
"""

from datetime import datetime
from xhtml2pdf import pisa
from io import BytesIO

def format_number_for_pdf(value):
    """为PDF格式化数值，使用HTML上标"""
    if value is None:
        return 'Not provided'
    
    try:
        num = float(value)
        
        # 处理零值
        if num == 0:
            return '0'
        
        # 处理整数
        if num == int(num) and abs(num) < 1000:
            return str(int(num))
        
        abs_num = abs(num)
        
        # 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
        if abs_num >= 1000 or (abs_num < 0.01 and abs_num != 0):
            # 使用标准的科学记数法格式，保留3位有效数字
            scientific_str = f"{num:.2e}"  # 保留2位小数
            mantissa, exponent_str = scientific_str.split('e')
            exponent = int(exponent_str)
            
            # 格式化为标准的10为底科学记数法，使用HTML上标
            return f"{mantissa} × 10<sup>{exponent}</sup>"
        
        # 对于正常范围的数值，保留3位有效数字
        return f"{float(f'{num:.3g}'):g}"
    except (ValueError, TypeError):
        return str(value)

def test_value_reference_center():
    """测试Value和Reference Range标签居中"""
    
    # 测试数据
    test_data = {
        'filename': 'value_reference_center_test.wav',
        'age': '74',
        'relationship': 'my_self',
        'occupation': 'retired'
    }
    
    test_result = {
        'Predicted mmse score': 22.9,
        'Percentile': 58,
        'Model': 'Value Reference Center Test Model',
        'Selected features': {
            'lexicosyntactic': [
                {
                    'feature name': 'word_count',
                    'value': 210,
                    'clsi lower': 160,
                    'clsi upper': 300,
                    'brief introduction': 'Total number of words spoken during the assessment'
                },
                {
                    'feature name': 'unique_words',
                    'value': 92,
                    'clsi lower': 70,
                    'clsi upper': 130,
                    'brief introduction': 'Number of unique words used, indicating vocabulary diversity'
                },
                {
                    'feature name': 'sentence_complexity',
                    'value': 0.00156,
                    'clsi lower': 0.0005,
                    'clsi upper': 0.005,
                    'brief introduction': 'Measure of syntactic complexity in sentence structure'
                }
            ],
            'digipsych_prosody_feats': [
                {
                    'feature name': 'speech_rate',
                    'value': 3.1,
                    'clsi lower': 2.2,
                    'clsi upper': 4.2,
                    'brief introduction': 'Rate of speech in words per second'
                },
                {
                    'feature name': 'pause_frequency',
                    'value': 0.22,
                    'clsi lower': 0.15,
                    'clsi upper': 0.35,
                    'brief introduction': 'Frequency of pauses during speech production'
                }
            ],
            'is10': [
                {
                    'feature name': 'f0_mean',
                    'value': 192.3,
                    'clsi lower': 170,
                    'clsi upper': 260,
                    'brief introduction': 'Mean fundamental frequency (pitch) of the voice'
                },
                {
                    'feature name': 'intensity_std',
                    'value': 15.7,
                    'clsi lower': 10,
                    'clsi upper': 25,
                    'brief introduction': 'Standard deviation of voice intensity levels'
                },
                {
                    'feature name': 'spectral_centroid',
                    'value': 0.0000789,
                    'clsi lower': 0.00002,
                    'clsi upper': 0.0002,
                    'brief introduction': 'Center of mass of the frequency spectrum'
                },
                {
                    'feature name': 'large_value_test',
                    'value': 4560,
                    'clsi lower': 3000,
                    'clsi upper': 6000,
                    'brief introduction': 'Test feature with large numerical value'
                }
            ]
        }
    }
    
    # 创建HTML模板 - 重点测试Value和Reference Range标签居中
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Value and Reference Range Center Test</title>
        <style>
            @page {{
                size: A4;
                margin: 1.5cm;
            }}
            
            body {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #EAEAEA;
                margin: 0;
                padding: 2rem;
                color: #000000;
            }}
            
            .report-page {{
                max-width: 900px;
                margin: 0 auto;
                background: #FFFFFF;
                border: 1px solid #000000;
            }}
            
            .report-header {{
                padding: 2rem;
                border-bottom: 2px solid #000000;
            }}
            
            .header-main {{
                text-align: center;
                margin-bottom: 2rem;
            }}
            
            .header-main h1 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.8rem;
                margin: 0 0 0.25rem 0;
                font-weight: bold;
            }}
            
            .header-main p {{
                font-size: 1rem;
                margin: 0;
                font-weight: bold;
                color: #333;
            }}
            
            .demographics-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                border: 1px solid #000;
            }}
            
            .demographics-table td {{
                padding: 0.35rem 0.5rem;
                border: 1px solid #CCC;
            }}
            
            .demographics-table .label {{
                font-weight: bold;
                white-space: nowrap;
                color: #000;
            }}
            
            .demographics-table .value {{
                color: #000;
            }}
            
            .report-body {{
                padding: 2rem;
                font-family: 'Times New Roman', Times, serif;
            }}
            
            .report-section {{
                margin-bottom: 3.5rem;
                page-break-inside: avoid;
            }}
            
            .section-title {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.6rem;
                font-weight: bold;
                color: #000;
                border-bottom: 2px solid #000;
                padding-bottom: 0.8rem;
                margin: 0 0 1.5rem 0;
                padding-top: 1rem;
            }}
            
            .features-grid {{
                margin-top: 2rem;
            }}
            
            .feature-category {{
                margin-bottom: 3rem;
                page-break-inside: avoid;
            }}
            
            .feature-category h4 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.4rem;
                font-weight: bold;
                margin: 0 0 1.5rem 0;
                color: #000;
                border-bottom: 1px solid #CCC;
                padding-bottom: 0.5rem;
            }}
            
            /* Feature Table: 重点测试Value和Reference Range居中 */
            .feature-table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 1.1rem;
                margin-bottom: 2rem;
                border: 1px solid #000;
            }}
            
            .feature-table th, .feature-table td {{
                border: 1px solid #E0E0E0;
                padding: 0.4rem;
                text-align: left;
            }}
            
            .feature-table th {{
                background-color: #F8F8F8;
                font-weight: bold;
                font-size: 1.2rem;
            }}
            
            /* 确保Value和Reference Range列的内容和标题都居中 */
            .feature-table td:nth-child(2), .feature-table th:nth-child(2) {{
                text-align: center;
                width: 20%;
                position: relative;
            }}
            
            .feature-table td:nth-child(3), .feature-table th:nth-child(3) {{
                text-align: center;
                width: 25%;
            }}
        </style>
    </head>
    <body>
        <div class="report-page">
            <header class="report-header">
                <div class="header-main">
                    <h1>Value and Reference Range Center Test</h1>
                    <p>Testing Centered Column Headers in Feature Analysis</p>
                </div>
                
                <table class="demographics-table">
                    <tbody>
                        <tr>
                            <td class="label">Audio File Name:</td>
                            <td class="value">{test_data.get('filename', 'N/A')}</td>
                            <td class="label">Date of Report:</td>
                            <td class="value">{datetime.now().strftime('%d-%m-%Y')}</td>
                        </tr>
                        <tr>
                            <td class="label">Age:</td>
                            <td class="value" colspan="3">{test_data.get('age', 'N/A')}</td>
                        </tr>
                    </tbody>
                </table>
            </header>

            <main class="report-body">
                <section class="report-section">
                    <h3 class="section-title">Feature Analysis</h3>
                    <div class="features-grid">"""
    
    # 添加特征分析部分
    features = test_result.get('Selected features', {})
    if features:
        # 处理特征类别
        category_keys = list(features.keys())
        if 'is10' in category_keys:
            category_keys.remove('is10')
            category_keys.append('is10')
        
        for category in category_keys:
            feature_list = features[category]
            if not isinstance(feature_list, list) or len(feature_list) == 0:
                continue
            
            # 映射类别名称
            category_name = category.replace('_', ' ')
            if category_name == 'digipsych prosody feats':
                category_name = 'Prosodic'
            elif category_name == 'lexicosyntactic':
                category_name = 'Linguistic'
            elif category_name == 'is10':
                category_name = 'Acoustic'
            
            html_template += f"""
                        <div class="feature-category">
                            <h4>{category_name} Features</h4>
                            <table class="feature-table">
                                <thead>
                                    <tr>
                                        <th>Feature</th>
                                        <th style="text-align: center;">Value</th>
                                        <th style="text-align: center;">Reference Range</th>
                                    </tr>
                                </thead>
                                <tbody>"""
            
            for feature_obj in feature_list:
                name = feature_obj.get('feature name', 'N/A')
                value = feature_obj.get('value', 'N/A')
                lower = feature_obj.get('clsi lower')
                upper = feature_obj.get('clsi upper')
                brief_intro = feature_obj.get('brief introduction', '')
                
                display_name = name.replace('_', ' ')
                
                # 检查是否异常
                indicator = ''
                if lower is not None and upper is not None and value != 'N/A':
                    try:
                        num_value = float(value)
                        num_lower = float(lower)
                        num_upper = float(upper)
                        if num_value > num_upper:
                            indicator = ' ▲'
                        elif num_value < num_lower:
                            indicator = ' ▼'
                    except (ValueError, TypeError):
                        pass
                
                # 格式化参考范围
                if lower is not None and upper is not None:
                    ref_range = f"{format_number_for_pdf(lower)} - {format_number_for_pdf(upper)}"
                else:
                    ref_range = 'Not provided'
                
                # 特征名称处理 - 包含简介
                feature_name_display = display_name
                if brief_intro:
                    feature_name_display += f"<br><small style='color: #666666; font-style: italic; font-size: 0.9em;'>{brief_intro}</small>"
                
                html_template += f"""
                                    <tr>
                                        <td>{feature_name_display}</td>
                                        <td style="text-align: center;"><strong>{format_number_for_pdf(value)}</strong>{indicator}</td>
                                        <td style="text-align: center;">{ref_range}</td>
                                    </tr>"""
            
            html_template += """
                                </tbody>
                            </table>
                        </div>"""
    
    # 添加页脚和总结
    html_template += f"""
                    </div>
                </section>
            </main>
        </div>
        
        <!-- Value和Reference Range居中总结 -->
        <div style="margin-top: 2rem; padding: 1rem; background: #f0f0f0; border: 1px solid #ccc;">
            <h3 style="margin: 0 0 1rem 0; color: #333;">Value和Reference Range标签居中显示：</h3>
            <ul style="margin: 0; padding-left: 1.5rem; color: #666;">
                <li><strong>修改前：</strong> &lt;th&gt;Value&lt;/th&gt; 和 &lt;th&gt;Reference Range&lt;/th&gt;</li>
                <li><strong>修改后：</strong> &lt;th style="text-align: center;"&gt;Value&lt;/th&gt; 和 &lt;th style="text-align: center;"&gt;Reference Range&lt;/th&gt;</li>
                <li><strong>效果：</strong></li>
                <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                    <li>Value标签居中显示</li>
                    <li>Reference Range标签居中显示</li>
                    <li>与列内容的居中对齐保持一致</li>
                    <li>提升表格的视觉对称性</li>
                </ul>
            </ul>
        </div>
    </body>
    </html>"""
    
    # 生成PDF
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html_template.encode("UTF-8")), result)
    
    if not pdf.err:
        return result.getvalue()
    else:
        raise Exception(f"PDF generation failed: {pdf.err}")

if __name__ == '__main__':
    try:
        pdf_bytes = test_value_reference_center()
        
        # 保存PDF文件
        output_filename = 'test_value_reference_center.pdf'
        with open(output_filename, 'wb') as f:
            f.write(pdf_bytes)
        
        print("✅ Value和Reference Range标签居中测试成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print(f"📁 文件保存为: {output_filename}")
        print("\n🔧 标签居中修改:")
        print("- Value标签: 添加 style='text-align: center;'")
        print("- Reference Range标签: 添加 style='text-align: center;'")
        print("\n📊 视觉效果:")
        print("- 表头标签与列内容对齐一致")
        print("- 提升了表格的视觉对称性")
        print("- 更加专业和整洁的外观")
        print("- 符合表格设计的最佳实践")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
