{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HiSage Health - AI Cognitive Health Analysis Platform{% endblock %}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="{% block description %}HiSage Health - AI-powered cognitive health analysis platform providing professional cognitive health assessment through speech analysis{% endblock %}">
    <meta name="keywords" content="cognitive health,AI analysis,speech analysis,health assessment,HiSage Health">
    <meta name="author" content="HiSage Health Team">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}HiSage Health - AI认知健康分析平台{% endblock %}">
    <meta property="og:description" content="{% block og_description %}基于AI的认知健康分析平台，通过语音分析提供专业的认知健康评估{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:image" content="{% static 'assets/logos/logo1.png' %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'assets/logos/logo1.png' %}">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <link rel="stylesheet" href="{% static 'css/index.css' %}">
    
    <style>
        /* 基础样式 */
        .header-links {
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-link {
            text-decoration: none;
            color: black;
        }
        
        .footer-link:hover {
            color: #2da44e;
        }
        
        /* 现代化用户Profile样式 */
        .user-profile {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .profile-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            object-fit: cover;
        }
        
        .profile-avatar:hover {
            border-color: #3b82f6;
            transform: scale(1.05);
        }
        
        .profile-dropdown {
            position: fixed;
            top: 60px;
            right: 20px;
            margin-top: 8px;
            min-width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            z-index: 9999;
        }
        
        .profile-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .profile-header {
            padding: 20px;
            border-bottom: 1px solid #f3f4f6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px 12px 0 0;
            color: white;
        }
        
        .profile-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .profile-avatar-large {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
        }
        
        .profile-details h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .profile-details p {
            margin: 4px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .profile-menu {
            padding: 8px 0;
        }
        
        .profile-menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.15s ease;
        }
        
        .profile-menu-item:hover {
            background-color: #f9fafb;
            color: #3b82f6;
        }
        
        .profile-menu-item i {
            width: 16px;
            text-align: center;
            font-size: 16px;
        }
        
        .profile-divider {
            height: 1px;
            background-color: #f3f4f6;
            margin: 8px 0;
        }
        
        .profile-menu-item.danger {
            color: #ef4444;
        }
        
        .profile-menu-item.danger:hover {
            background-color: #fef2f2;
            color: #dc2626;
        }
        
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .auth-btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .auth-btn.signin {
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .auth-btn.signin:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
        }
        
        .auth-btn.signup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .auth-btn.signup:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        /* 下拉菜单遮罩 */
        .profile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: 9998;
            display: none;
        }

        .profile-overlay.show {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .profile-dropdown {
                min-width: 260px;
                right: 10px !important;
                left: 10px !important;
                width: calc(100% - 20px);
                max-width: 320px;
            }

            .profile-header {
                padding: 16px;
            }

            .profile-avatar-large {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>

<body class="tw-bg-gray-50">
    <!-- 通知栏 -->
    <div id="notification-bar" class="tw-hidden tw-fixed tw-top-0 tw-left-0 tw-w-full tw-bg-blue-600 tw-text-white tw-py-2 tw-px-4 tw-z-50">
        <div class="tw-flex tw-justify-between tw-items-center tw-max-w-6xl tw-mx-auto">
          <span id="notification-text"></span>
          <button type="button" class="bi bi-x tw-text-lg" aria-label="Close"></button>
        </div>
    </div>

    <!-- 导航栏 -->
    <header class="tw-flex tw-w-full tw-z-20 tw-h-[60px] lg:tw-justify-around tw-absolute tw-top-0 tw-px-[10%] max-lg:tw-mr-auto tw-bg-white tw-shadow-sm">
        <!-- Logo -->
        <a class="tw-w-[50px] tw-h-[50px] tw-p-[4px]" href="{% url 'home' %}">
            <img src="{% static 'assets/logos/logo1.png' %}" alt="HiSage Health Logo" class="tw-w-full tw-h-full tw-object-contain">
        </a>
        
        <!-- 导航内容 -->
        <div class="collapsable-header animated-collapse max-lg:tw-shadow-md" id="collapsed-header-items">
            <!-- 导航链接 -->
            <div class="tw-w-max tw-text-base tw-flex tw-gap-5 tw-h-full lg:tw-mx-auto lg:tw-place-items-center max-lg:tw-flex-col max-lg:tw-w-full max-lg:tw-mt-[50px] max-lg:tw-gap-5 tw-text-black">
                <a class="header-links" href="{% url 'home' %}">About us</a>
                <a class="header-links" href="{% url 'blogs' %}" rel="noreferrer">Blogs</a>
                <a class="header-links" href="{% url 'contact-us' %}" rel="noreferrer">Contact us</a>
            </div>
            
            <!-- 右侧按钮区域 -->
            <div class="tw-flex tw-gap-5 tw-place-items-center max-lg:tw-flex-col max-lg:tw-w-full max-lg:tw-gap-5">
                <!-- 社交媒体链接 -->
                <div class="tw-flex tw-gap-3">
                    <a href="https://www.facebook.com/" target="_blank" rel="noreferrer" aria-label="facebook" class="header-links tw-transition-colors tw-duration-[0.3s]">
                        <i class="bi bi-facebook"></i>
                    </a>
                    <a href="https://www.instagram.com/" target="_blank" rel="noreferrer" aria-label="instagram" class="header-links tw-transition-colors tw-duration-[0.3s]">
                        <i class="bi bi-instagram"></i>
                    </a>
                    <a href="https://github.com/PaulleDemon" target="_blank" rel="noreferrer" aria-label="github" class="header-links tw-transition-colors tw-duration-[0.3s]">
                        <i class="bi bi-github"></i>
                    </a>
                </div>

                <!-- 认证按钮/用户Profile -->
                <div id="auth-section">
                    <!-- 未登录状态 -->
                    <div id="auth-buttons" class="auth-buttons">
                        <a href="{% url 'login' %}" class="auth-btn signin">Sign in</a>
                        <a href="{% url 'register' %}" class="auth-btn signup">Sign up</a>
                    </div>

                    <!-- 已登录状态 -->
                    <div id="user-profile" class="user-profile" style="display: none;">
                        <img id="profile-avatar" src="{% static 'assets/images/default-avatar.svg' %}" alt="User Avatar" class="profile-avatar">
                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button class="tw-absolute tw-text-black tw-z-50 tw-right-3 tw-top-3 tw-text-3xl bi bi-list lg:tw-hidden"
                onclick="toggleHeader()" aria-label="menu" id="collapse-btn">
        </button>
    </header>

    <!-- Profile下拉菜单（独立于header，用于定位） -->
    <div id="profile-overlay" class="profile-overlay"></div>
    <div id="profile-dropdown" class="profile-dropdown">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-info">
                <img id="profile-avatar-large" src="{% static 'assets/images/default-avatar.svg' %}" alt="User Avatar" class="profile-avatar-large">
                <div class="profile-details">
                    <h3 id="profile-name">User Name</h3>
                    <p id="profile-email"><EMAIL></p>
                </div>
            </div>
        </div>
        
        <!-- Menu Items -->
        <div class="profile-menu">
            <a href="/user/profile/" class="profile-menu-item">
                <i class="bi bi-person"></i>
                <span>Profile</span>
            </a>
            <a href="/audio_upload/history/" class="profile-menu-item">
                <i class="bi bi-clock-history"></i>
                <span>Audio Analysis History</span>
            </a>
            <a href="/user/settings/" class="profile-menu-item">
                <i class="bi bi-gear"></i>
                <span>Settings</span>
            </a>
            <div class="profile-divider"></div>
            <a href="#" id="logout-btn" class="profile-menu-item danger">
                <i class="bi bi-box-arrow-right"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="tw-w-full tw-h-full tw-mt-[50px]">
        {% block content %}{% endblock content %}
    </div>

    <!-- 页脚 -->
    <footer class="tw-flex max-md:tw-flex-col tw-w-full tw-p-[5%] tw-bg-gray-100 tw-mt-20">
        {% block footer %}
        <div class="tw-flex tw-flex-col tw-gap-5 tw-w-full">
            <div class="tw-flex max-md:tw-flex-col tw-gap-10 tw-w-full">
                <div class="tw-flex tw-flex-col tw-gap-3 tw-w-full">
                    <h3 class="tw-text-lg tw-font-semibold">HiSage Health</h3>
                    <p class="tw-text-gray-600">AI驱动的认知健康分析平台</p>
                </div>
                
                <div class="tw-flex tw-flex-col tw-gap-3 tw-w-full">
                    <h4 class="tw-font-semibold">Quick Links</h4>
                    <a href="{% url 'home' %}" class="footer-link">About us</a>
                    <a href="{% url 'blogs' %}" class="footer-link">Blogs</a>
                    <a href="{% url 'contact-us' %}" class="footer-link">Contact us</a>
                </div>
            </div>
            
            <div class="tw-border-t tw-pt-5 tw-text-center tw-text-gray-600">
                <p>&copy; 2024 HiSage Health. All rights reserved.</p>
            </div>
        </div>
        {% endblock footer %}
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- API配置 -->
    <script>
        // 从Django传递API配置到前端
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
    </script>

    <!-- 用户Profile功能 -->
    <script>
        class UserProfile {
            constructor() {
                this.authButtons = document.getElementById('auth-buttons');
                this.userProfile = document.getElementById('user-profile');
                this.profileAvatar = document.getElementById('profile-avatar');
                this.profileAvatarLarge = document.getElementById('profile-avatar-large');
                this.profileDropdown = document.getElementById('profile-dropdown');
                this.profileOverlay = document.getElementById('profile-overlay');
                this.profileName = document.getElementById('profile-name');
                this.profileEmail = document.getElementById('profile-email');
                this.logoutBtn = document.getElementById('logout-btn');

                this.isDropdownOpen = false;
                this.init();
            }

            init() {
                // 检查登录状态
                this.checkAuthStatus();

                // 绑定事件
                this.bindEvents();
            }

            bindEvents() {
                // 点击头像切换下拉菜单
                this.profileAvatar?.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown();
                });

                // 点击遮罩层关闭下拉菜单
                this.profileOverlay?.addEventListener('click', () => {
                    this.closeDropdown();
                });

                // 点击页面其他地方关闭下拉菜单
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('#user-profile') && !e.target.closest('#profile-dropdown')) {
                        this.closeDropdown();
                    }
                });

                // ESC键关闭下拉菜单
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.closeDropdown();
                    }
                });

                // 登出按钮
                this.logoutBtn?.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.logout();
                });

                // 窗口大小改变时重新定位
                window.addEventListener('resize', () => {
                    if (this.isDropdownOpen) {
                        this.positionDropdown();
                    }
                });
            }

            toggleDropdown() {
                if (this.isDropdownOpen) {
                    this.closeDropdown();
                } else {
                    this.openDropdown();
                }
            }

            openDropdown() {
                this.isDropdownOpen = true;
                this.profileDropdown.classList.add('show');
                this.profileOverlay.classList.add('show');
                this.profileAvatar.setAttribute('aria-expanded', 'true');
                this.positionDropdown();
            }

            closeDropdown() {
                this.isDropdownOpen = false;
                this.profileDropdown.classList.remove('show');
                this.profileOverlay.classList.remove('show');
                this.profileAvatar.setAttribute('aria-expanded', 'false');
            }

            positionDropdown() {
                // 获取头像的位置
                const rect = this.profileAvatar.getBoundingClientRect();
                const dropdownWidth = 280; // 下拉菜单宽度
                const margin = 20; // 边距

                // 计算下拉菜单位置
                let top = rect.bottom + 8; // 头像下方8px
                let right = window.innerWidth - rect.right; // 右对齐头像

                // 确保不超出屏幕右边界
                if (right < margin) {
                    right = margin;
                }

                // 确保不超出屏幕左边界
                if (window.innerWidth - right - dropdownWidth < margin) {
                    right = window.innerWidth - dropdownWidth - margin;
                }

                // 确保不超出屏幕下边界
                if (top + 400 > window.innerHeight) { // 假设下拉菜单高度约400px
                    top = rect.top - 8 - 400; // 显示在头像上方
                }

                // 应用位置
                this.profileDropdown.style.top = `${top}px`;
                this.profileDropdown.style.right = `${right}px`;
                this.profileDropdown.style.left = 'auto';
                this.profileDropdown.style.bottom = 'auto';
            }

            checkAuthStatus() {
                const token = localStorage.getItem('access_token');
                console.log('Checking auth status, token:', token ? 'exists' : 'not found');

                if (token && !this.isTokenExpired(token)) {
                    console.log('Token valid, showing user profile');
                    this.showUserProfile();
                    this.fetchUserData();
                } else {
                    console.log('No valid token, showing auth buttons');
                    this.showAuthButtons();
                }
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp * 1000 < Date.now();
                } catch (e) {
                    return true;
                }
            }

            showUserProfile() {
                this.authButtons.style.display = 'none';
                this.userProfile.style.display = 'inline-flex';
            }

            showAuthButtons() {
                this.authButtons.style.display = 'flex';
                this.userProfile.style.display = 'none';
            }

            async fetchUserData() {
                try {
                    const token = localStorage.getItem('access_token');
                    const apiBaseUrl = '{{ API_BASE_URL }}';
                    console.log('Fetching user data from:', `${apiBaseUrl}/api/user/profile/`);

                    const response = await fetch(`${apiBaseUrl}/api/user/profile/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('Profile API response status:', response.status);

                    if (response.ok) {
                        const userData = await response.json();
                        console.log('User data received:', userData);
                        this.updateUserProfile(userData);
                    } else {
                        // 如果API调用失败，使用默认用户信息
                        console.log('Profile API failed, using default user info');
                        this.updateUserProfile({
                            display_name: '用户',
                            email: '<EMAIL>',
                            avatar: null
                        });
                    }
                } catch (error) {
                    console.error('Error fetching user data:', error);
                    // 即使API失败，也显示用户profile（使用默认信息）
                    this.updateUserProfile({
                        display_name: '用户',
                        email: '<EMAIL>',
                        avatar: null
                    });
                }
            }

            updateUserProfile(userData) {
                console.log('Updating user profile with data:', userData);

                // 处理API响应格式（可能包含data字段）
                const user = userData.data || userData;

                // 更新头像
                const avatarUrl = user.avatar || '{% static "assets/images/default-avatar.svg" %}';
                this.profileAvatar.src = avatarUrl;
                this.profileAvatarLarge.src = avatarUrl;

                // 更新用户信息
                this.profileName.textContent = user.display_name || user.full_name || '用户';
                this.profileEmail.textContent = user.email || '';

                console.log('Profile updated - Name:', this.profileName.textContent, 'Email:', this.profileEmail.textContent);
            }

            async logout() {
                try {
                    const token = localStorage.getItem('access_token');
                    const refreshToken = localStorage.getItem('refresh_token');
                    const apiBaseUrl = '{{ API_BASE_URL }}';

                    // 发送登出请求
                    await fetch(`${apiBaseUrl}/api/logout/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({
                            refresh_token: refreshToken
                        })
                    });
                } catch (error) {
                    console.error('Logout error:', error);
                } finally {
                    // 清除本地存储
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');

                    // 关闭下拉菜单
                    this.closeDropdown();

                    // 显示登录按钮
                    this.showAuthButtons();

                    // 重定向到首页
                    window.location.href = '{% url "home" %}';
                }
            }
        }

        // 移动端菜单切换
        function toggleHeader() {
            const headerItems = document.getElementById('collapsed-header-items');
            const collapseBtn = document.getElementById('collapse-btn');

            headerItems.classList.toggle('tw-hidden');
            collapseBtn.classList.toggle('bi-list');
            collapseBtn.classList.toggle('bi-x');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing UserProfile');
            const userProfile = new UserProfile();
            window.userProfile = userProfile; // 全局访问

            // 调试信息
            console.log('Auth buttons element:', document.getElementById('auth-buttons'));
            console.log('User profile element:', document.getElementById('user-profile'));
            console.log('Current token:', localStorage.getItem('access_token') ? 'exists' : 'not found');
        });
    </script>

    {% block extra_js %}{% endblock extra_js %}
</body>
</html>
