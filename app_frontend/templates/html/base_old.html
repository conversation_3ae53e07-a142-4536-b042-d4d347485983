{% load static %}
{% load django_browser_reload %}

{% load custom_tags %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="shortcut icon" href="{% static 'assets/logos/logo.png' %}" type="image/png">
    
    <meta name="theme-color" content="#ffffff">

    <title>{% block title %}{% endblock title %} {% settings_value "PROJECT_TITLE" %}</title>
    <meta name="description" 
            content="{% block description %}Building websites that brings you business{% endblock description %}">

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="{% block socialTitle %}{% endblock socialTitle %} Project" />
    <meta property="og:description" 
                        content="{% block socialDescription %}Description Here{% endblock socialDescription %}" />
    <meta property="og:type" content="{% block pageType %}website{% endblock pageType %}" />
    <meta property="og:url" content="{% block pageLink %}{{request.build_absolute_uri}}{% endblock pageLink %}" />
    <meta property="og:image" content="{% block pageImage %}{{ request.scheme }}://{{request.get_host}}{% static "./assets/images/home/<USER>" %}{% endblock pageImage %}" />

    <!-- Twitter -->
    {% comment %} 
    `some of the meta tags specific to twitter has been dropped in favor of OG tags, Since twitter also supports OG tags as fallback
    https://developer.twitter.com/en/docs/twitter-for-websites/cards/guides/getting-started
    {% endcomment %}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@">
    {% comment %} <meta name="twitter:title" content="{% block twitterTitle %}Project{% endblock twitterTitle %}">
    <meta name="twitter:description" content="{% block twitterDescription %}Description Here{% endblock socialDescription %}"> {% endcomment %}
    {% comment %} <meta name="twitter:image" content="{% block pageImage %}http://www.example.com/image.jpg{% endblock pageImage %}"> {% endcomment %}
    
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <link rel="stylesheet" href="{% static "./css/index.css" %}">
    
    <style>
        .header-links {
            text-decoration: none;
        }
        .footer-link {
            text-decoration: none;
            color: black;
        }
        .footer-link:hover {
            color: #2da44e;
        }
        
        /* GitHub风格的头像和下拉菜单 */
        .avatar-wrapper {
            position: relative;
            display: inline-block;
        }
        
        .avatar-button {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            transition: transform 0.15s ease;
        }
        
        .avatar-button:hover {
            transform: scale(1.1);
        }
        
        .avatar-button:focus {
            outline: none;
        }
        
        .avatar-image {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: block;
        }
        
        .dropdown-menu {
            position: fixed;  /* 改为fixed定位 */
            min-width: 240px;
            background: white;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            box-shadow: 0 8px 24px rgba(140,149,159,0.2);
            z-index: 99999;
            display: none;
            animation: dropdownFadeIn 0.1s ease-out;
        }
        
        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-4px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .dropdown-header {
            padding: 16px;
            border-bottom: 1px solid #d0d7de;
        }
        
        .dropdown-header-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 600;
            color: #24292f;
            font-size: 14px;
        }
        
        .user-email {
            color: #57606a;
            font-size: 12px;
        }
        
        .dropdown-item {
            display: block;
            padding: 8px 16px;
            color: #24292f;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.1s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f6f8fa;
        }
        
        .dropdown-divider {
            height: 1px;
            background-color: #d0d7de;
            margin: 8px 0;
        }
        
        .dropdown-item-danger {
            color: #cf222e;
        }
        
        .dropdown-item-danger:hover {
            color: #a40e26;
            background-color: #f6f8fa;
        }
    </style>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js" integrity="sha512-2rNj2KJ+D8s1ceNasTIex6z4HWyOnEYLVC3FigGOmyQCZc2eBXKgOxQmo3oKLHyfcj53uz4QMsRCWNbLd32Q1g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>


    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={% settings_value "ANALYTICS_TAG_ID" %}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', '{% settings_value "ANALYTICS_TAG_ID" %}');
    </script>

    {% block head_tags %}
    {% endblock head_tags %}

</head>
    
{% comment %} <body class="tw-min-h-[100vh]" data-bs-theme="dark"> {% endcomment %}
<body class="tw-min-h-[100vh] tw-w-full tw-bg-[#ffffff] tw-flex tw-flex-col">
    
    <div id="toast" class="tw-p-2 tw-px-4 tw-bg-black tw-h-max tw-z-[5000] tw-fixed 
                            tw-top-[5%] tw-left-[50%] tw-translate-x-[-50%] 
                            tw-place-items-center
                            tw-hidden tw-rounded-md
                            tw-border-0 tw-text-white" 
                    role="alert">
        <div class="tw-flex tw-gap-1">
          <div class="toast-body" id="toast-body">
           
          </div>
          <button type="button" class="bi bi-x tw-text-lg" aria-label="Close"></button>
        </div>
    </div>

    <header class="tw-flex tw-w-full tw-z-20
                    tw-h-[60px]  
                    lg:tw-justify-around
                    tw-absolute tw-top-0 tw-px-[10%]
                    max-lg:tw-mr-auto
                    tw-bg-white
                    tw-text-black
                    ">

        <a class="tw-w-[50px] tw-h-[50px] tw-p-[4px]" href="{% url "home" %}">
            <img src="{% static "assets/logos/logo1.png" %}" 
                  alt="logo" class="tw-w-full tw-h-full tw-object-contain">

        </a>
        <div class="collapsable-header animated-collapse max-lg:tw-shadow-md"
                    id="collapsed-header-items"
                    >
            <div class=" tw-w-max
                            tw-text-base 
                            tw-flex tw-gap-5 tw-h-full lg:tw-mx-auto
                            lg:tw-place-items-center 
                            max-lg:tw-place-items-end
                            max-lg:tw-flex-col
                            max-lg:tw-mt-[50px]
                            max-lg:tw-gap-5
                            tw-text-black
                        ">
                    
                <a class="header-links" href="{% url "home" %}">
                    About us
                </a>
                <a class="header-links" href="{% url "blogs" %}"
                        rel="noreferrer"
                        >
                    Blogs
                </a>
                <a class="header-links" href="{% url "contact-us" %}"
                        rel="noreferrer"
                        >
                    Contact us
                </a>
            </div>
            <div class="tw-flex tw-gap-[20px] tw-place-items-center tw-text-xl
                         max-lg:!tw-text-white
                         max-lg:tw-place-content-center
                        max-lg:tw-w-full
                         max-lg:tw-place-items-start
                         ">
                <a href="https://www.facebook.com/" 
                        target="_blank" 
                        rel="noreferrer"
                        aria-label="facebook"
                        class="header-links tw-transition-colors tw-duration-[0.3s]">
                    <i class="bi bi-facebook"></i>
                </a>

                <a href="https://www.instagram.com/"
                        target="_blank"
                        rel="noreferrer"
                        aria-label="instagram"
                        class="header-links tw-transition-colors tw-duration-[0.3s]">
                    <i class="bi bi-instagram"></i>
                </a>
                <a href="https://github.com/PaulleDemon"
                        target="_blank"
                        rel="noreferrer"
                        aria-label="github"
                        class="header-links tw-transition-colors tw-duration-[0.3s]">
                    <i class="bi bi-github"></i>
                </a>

                <!-- 登录按钮组 -->
                <div id="auth-buttons" style="display: flex; gap: 8px;">
                    <a href="{% url 'login' %}" class="header-links">Sign in</a>
                    <a href="{% url 'register' %}" class="header-links">Sign up</a>
                </div>

                <!-- 头像下拉菜单 -->
                <div id="avatar-wrapper" class="avatar-wrapper" style="display: none;">
                    <button id="avatar-button" class="avatar-button" aria-haspopup="true" aria-expanded="false">
                        <img id="avatar-image" src="/static/assets/images/default-avatar.png" alt="User avatar" class="avatar-image">
                    </button>
                    
                    <div id="dropdown-menu" class="dropdown-menu">
                        <div class="dropdown-header">
                            <div class="dropdown-header-content">
                                <img id="dropdown-avatar" src="/static/assets/images/default-avatar.png" alt="User avatar" class="avatar-image">
                                <div class="user-info">
                                    <div id="dropdown-username" class="user-name">User</div>
                                    <div id="dropdown-email" class="user-email"><EMAIL></div>
                                </div>
                            </div>
                        </div>
                        
                        <a href="/user/profile/" class="dropdown-item">
                            <i class="bi bi-person me-2"></i>Profile
                        </a>
                        <a href="/audio_upload/#history" class="dropdown-item">
                            <i class="bi bi-clock-history me-2"></i>Audio Analysis History
                        </a>
                        <a href="/user/settings/" class="dropdown-item">
                            <i class="bi bi-gear me-2"></i>Settings
                        </a>
                        
                        <div class="dropdown-divider"></div>
                        
                        <a href="#" class="dropdown-item dropdown-item-danger" id="logout-button">
                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <button class="tw-absolute tw-text-black tw-z-50 
                        tw-right-3
                        tw-top-3
                        
                         tw-text-3xl  bi bi-list lg:tw-hidden" 
                onclick="toggleHeader()" aria-label="menu" id="collapse-btn">
            <!-- <i class="bi bi-list"></i> -->
        </button>
    </header>

    <div class="tw-w-full tw-h-full tw-mt-[50px]">
        {% block content %}{% endblock content %}
    </div>
    <footer class="tw-flex max-md:tw-flex-col tw-w-full tw-p-[5%]
                    tw-px-[10%] tw-place-content-around tw-gap-3 
                    tw-text-black
                    tw-mt-auto
                    ">
        <div class="tw-h-full tw-w-[250px] tw-flex tw-flex-col 
                    tw-gap-6 tw-place-items-center max-md:tw-w-full">
            
            <img src="{% static "assets/logos/logo1.png" %}" 
                alt="logo"  class="tw-max-w-[150px] max-md:tw-max-w-[120px]">
            <div>
                2 Lord Edward St,   
                <br>
                D02 P634,
                <br>
                United States
            </div>
            <div class="tw-mt-3 tw-font-semibold tw-text-lg">
                Follow us
            </div>
            <div class="tw-flex tw-gap-4 tw-text-2xl">
                <a href="https://facebook.com/" aria-label="Facebook">
                    <i class="bi bi-facebook"></i>
                </a>
                <a href="https://twitter.com/pauls_freeman" aria-label="Twitter">
                    <i class="bi bi-twitter"></i>
                </a>
                <a href="https://instagram.com/" class="tw-w-[40px] tw-h-[40px]" aria-label="Instagram">
                   <i class="bi bi-instagram"></i>
                </a>
            </div>
        </div>

        <div class="tw-h-full tw-w-[250px] tw-flex tw-flex-col 
                    tw-gap-4">

            <h2 class="tw-text-3xl max-md:tw-text-xl">
                Resources
            </h2>
            <div class=" tw-flex tw-flex-col tw-gap-3 max-md:tw-text-sm">
                <a href="{% url "home" %}" class="footer-link">About us</a>
                <a href="" class="footer-link">FAQ</a>
                <a href="{% url "contact-us" %}" class="footer-link">Contact Us</a>
                <a href="" class="footer-link">Privacy policy</a>
            </div>

        </div>

        {% block footer %}
        {% endblock footer %}

    </footer>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
      const avatarBtn = document.getElementById('github-avatar-btn');
      const dropdown = document.getElementById('github-dropdown');

      // 动态定位菜单
      function positionDropdown() {
        const rect = avatarBtn.getBoundingClientRect();
        dropdown.style.left = (rect.left + window.scrollX) + 'px';
        dropdown.style.top = (rect.bottom + window.scrollY + 4) + 'px';
      }

      // 切换菜单
      avatarBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        const expanded = avatarBtn.getAttribute('aria-expanded') === 'true';
        avatarBtn.setAttribute('aria-expanded', !expanded);
        if (dropdown.style.display === 'block') {
          dropdown.style.display = 'none';
        } else {
          positionDropdown();
          dropdown.style.display = 'block';
        }
      });

      // 点击外部关闭
      document.addEventListener('click', function(e) {
        if (dropdown.style.display === 'block') {
          dropdown.style.display = 'none';
          avatarBtn.setAttribute('aria-expanded', 'false');
        }
      });

      // ESC关闭
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          dropdown.style.display = 'none';
          avatarBtn.setAttribute('aria-expanded', 'false');
        }
      });

      // 菜单项跳转
      document.getElementById('github-profile-link').onclick = function(e) {
        e.preventDefault();
        dropdown.style.display = 'none';
        window.location.href = '/user/profile/';
      };
      document.getElementById('github-history-link').onclick = function(e) {
        e.preventDefault();
        dropdown.style.display = 'none';
        window.location.href = '/audio_upload/#history';
      };
      document.getElementById('github-settings-link').onclick = function(e) {
        e.preventDefault();
        dropdown.style.display = 'none';
        window.location.href = '/user/settings/';
      };
      document.getElementById('github-logout-link').onclick = function(e) {
        e.preventDefault();
        dropdown.style.display = 'none';
        logout();
      };

      // 窗口resize和scroll时重新定位
      window.addEventListener('resize', function() {
        if (dropdown.style.display === 'block') positionDropdown();
      });
      window.addEventListener('scroll', function() {
        if (dropdown.style.display === 'block') positionDropdown();
      });
    });
    </script>
</body>

{% comment %} <script src="{% static "./js/base.js" %}"></script> {% endcomment %}
<script>
    // Timezone settings
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone; // e.g. "America/New_York"
    document.cookie = "user_timezone=" + timezone;
</script>
{% comment %} <script src="https://unpkg.com/quill-paste-smart@latest/dist/quill-paste-smart.js"></script> {% endcomment %}

<script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js" integrity="sha512-WFN04846sdKMIP5LKNphMaWzU7YpMyCU245etK3g/2ARYbPK9Ub18eG+ljU96qKRCWh+quCY7yefSmlkQw1ANQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.1/dist/browser-image-compression.js"></script>

<script src="https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js"></script>
<script>
function isTokenExpired(token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    if (!payload.exp) return true;
    const now = Math.floor(Date.now() / 1000);
    return payload.exp < now;
  } catch (e) {
    return true;
  }
}

function showAvatarMenu() {
    document.getElementById('auth-buttons').style.display = 'none';
    document.getElementById('avatar-wrapper').style.display = 'inline-block';
}

function hideAvatarMenu() {
    document.getElementById('auth-buttons').style.display = 'flex';
    document.getElementById('avatar-wrapper').style.display = 'none';
}

function updateAvatarMenu(user) {
    const avatarUrl = user.avatar_url || '/static/assets/images/default-avatar.png';
    document.getElementById('avatar-image').src = avatarUrl;
    document.getElementById('dropdown-avatar').src = avatarUrl;
    document.getElementById('dropdown-username').textContent = user.username || user.name || user.email || 'User';
    document.getElementById('dropdown-email').textContent = user.email || '';
}

function logout() {
    const token = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');
    
    if (!token) {
        window.location.href = '{% url "login" %}';
        return;
    }

    // 发送登出请求到后端
    const apiBaseUrl = '{{ API_BASE_URL }}';
    fetch(`${apiBaseUrl}/api/logout/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            refresh_token: refreshToken
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Logout failed');
        }
        return response.json();
    })
    .then(() => {
        // 清除本地存储的token
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        // 隐藏头像菜单
        hideAvatarMenu();
        // 重定向到登录页面
        window.location.href = '{% url "home" %}';
    })
    .catch(error => {
        console.error('Error during logout:', error);
        // 即使请求失败也清除本地token并重定向
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        hideAvatarMenu();
        window.location.href = '{% url "home" %}';
    });
}

function checkAuthUI() {
    const token = localStorage.getItem('access_token');
    if (token && !isTokenExpired(token)) {
        showAvatarMenu();
        const apiBaseUrl = '{{ API_BASE_URL }}';
        fetch(`${apiBaseUrl}/api/profile/`, {
            headers: { 'Authorization': 'Bearer ' + token }
        })
        .then(res => res.json())
        .then(data => updateAvatarMenu(data))
        .catch(err => console.error('Failed to fetch user profile:', err));
    } else {
        hideAvatarMenu();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    checkAuthUI();

    const avatarButton = document.getElementById('avatar-button');
    const dropdownMenu = document.getElementById('dropdown-menu');
    let isDropdownOpen = false;

    function positionDropdown() {
        const buttonRect = avatarButton.getBoundingClientRect();
        dropdownMenu.style.top = (buttonRect.bottom + 8) + 'px';
        dropdownMenu.style.left = (buttonRect.right - dropdownMenu.offsetWidth) + 'px';
    }

    function toggleDropdown(event) {
        event.stopPropagation();
        isDropdownOpen = !isDropdownOpen;
        
        if (isDropdownOpen) {
            dropdownMenu.style.display = 'block';
            positionDropdown();
            avatarButton.setAttribute('aria-expanded', 'true');
        } else {
            dropdownMenu.style.display = 'none';
            avatarButton.setAttribute('aria-expanded', 'false');
        }
    }

    function closeDropdown() {
        if (isDropdownOpen) {
            isDropdownOpen = false;
            dropdownMenu.style.display = 'none';
            avatarButton.setAttribute('aria-expanded', 'false');
        }
    }

    // 点击头像切换下拉菜单
    avatarButton.addEventListener('click', toggleDropdown);

    // 点击页面其他地方关闭下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('#avatar-wrapper')) {
            closeDropdown();
        }
    });

    // 按ESC键关闭下拉菜单
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeDropdown();
        }
    });

    // 处理登出按钮点击
    document.getElementById('logout-button').addEventListener('click', function(event) {
        event.preventDefault();
        closeDropdown();
        logout();
    });

    // 窗口大小改变时重新定位下拉菜单
    window.addEventListener('resize', function() {
        if (isDropdownOpen) {
            positionDropdown();
        }
    });

    // 页面滚动时重新定位下拉菜单
    window.addEventListener('scroll', function() {
        if (isDropdownOpen) {
            positionDropdown();
        }
    });
});
</script>

<!-- GitHub风格下拉菜单，直接挂body下，初始隐藏 -->
<div id="github-dropdown" class="github-dropdown-menu tw-hidden" style="position:absolute;z-index:99999;min-width:220px;background:#fff;border-radius:8px;box-shadow:0 8px 24px rgba(140,149,159,0.2);border:1px solid #d0d7de;padding:0.5rem 0;display:none;">
  <div style="padding:12px 16px;border-bottom:1px solid #f6f8fa;display:flex;align-items:center;gap:12px;">
    <img id="github-avatar-small" src="/static/assets/images/default-avatar.png" width="36" height="36" style="border-radius:50%;">
    <div style="flex:1;">
      <div id="github-username" style="font-weight:600;">User</div>
      <div id="github-email" style="font-size:13px;color:#57606a;"><EMAIL></div>
    </div>
  </div>
  <a href="#" id="github-profile-link" style="display:block;padding:10px 16px;text-decoration:none;color:#24292f;font-size:15px;">Profile</a>
  <a href="#" id="github-history-link" style="display:block;padding:10px 16px;text-decoration:none;color:#24292f;font-size:15px;">Audio Analysis History</a>
  <a href="#" id="github-settings-link" style="display:block;padding:10px 16px;text-decoration:none;color:#24292f;font-size:15px;">Settings</a>
  <div style="border-top:1px solid #f6f8fa;margin:4px 0;"></div>
  <a href="#" id="github-logout-link" style="display:block;padding:10px 16px;text-decoration:none;color:#d73a49;font-size:15px;">Logout</a>
</div>

{% block scripts %}{% endblock scripts %}

</html>