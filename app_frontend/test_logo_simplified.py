#!/usr/bin/env python3
"""
测试简化后的logo功能
"""

import os
import sys
import django
import base64
from datetime import datetime
from xhtml2pdf import pisa
from io import BytesIO

# 设置Django环境
try:
    sys.path.append('.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
    django.setup()
    from audio_upload.views import generate_pdf_with_xhtml2pdf, get_logo_base64
    from project.settings import LOCAL_BASE_URL
    DJANGO_AVAILABLE = True
    print(f"✅ Django环境可用")
except Exception as e:
    DJANGO_AVAILABLE = False
    LOCAL_BASE_URL = "http://localhost:8000"
    print(f"⚠️  Django环境不可用: {e}")

def test_logo_loading():
    """测试logo加载功能"""
    print("\n🔍 测试logo加载...")
    
    # 直接测试logo文件
    logo_path = 'audio_upload/static/logos/logo1.png'
    print(f"📂 检查logo文件: {logo_path}")
    
    if os.path.exists(logo_path):
        try:
            with open(logo_path, 'rb') as f:
                logo_data = f.read()
            logo_base64 = base64.b64encode(logo_data).decode('utf-8')
            print(f"✅ Logo文件存在")
            print(f"📊 文件大小: {len(logo_data)} bytes")
            print(f"📊 Base64长度: {len(logo_base64)}")
            print(f"📊 Base64前缀: {logo_base64[:50]}...")
            return logo_base64
        except Exception as e:
            print(f"❌ 读取logo失败: {e}")
            return ""
    else:
        print(f"❌ Logo文件不存在: {logo_path}")
        return ""

def test_django_logo_function():
    """测试Django环境下的logo函数"""
    if DJANGO_AVAILABLE:
        try:
            print("\n🔍 测试Django logo函数...")
            logo_base64 = get_logo_base64()
            if logo_base64:
                print(f"✅ Django logo函数成功")
                print(f"📊 返回Base64长度: {len(logo_base64)}")
                return logo_base64
            else:
                print("❌ Django logo函数返回空值")
                return ""
        except Exception as e:
            print(f"❌ Django logo函数失败: {e}")
            return ""
    else:
        print("⚠️  Django环境不可用，跳过Django logo函数测试")
        return ""

def create_simple_logo_test():
    """创建简单的logo测试PDF"""
    print("\n🔄 创建简单logo测试PDF...")
    
    # 获取logo
    logo_base64 = test_logo_loading()
    
    # 创建简化的HTML模板，完全模仿audio_details.html的结构
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Simple Logo Test</title>
        <style>
            @page {{
                size: A4;
                margin: 1.5cm;
            }}
            
            body {{
                font-family: 'Times New Roman', Times, serif;
                background-color: #EAEAEA;
                margin: 0;
                padding: 2rem;
                color: #000000;
            }}
            
            .report-page {{
                max-width: 900px;
                margin: 0 auto;
                background: #FFFFFF;
                border: 1px solid #000000;
            }}
            
            .report-header {{
                padding: 2rem;
                border-bottom: 2px solid #000000;
            }}
            
            .header-top {{
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                margin-bottom: 2rem;
            }}
            
            /* 完全复制audio_details.html的logo样式 */
            .header-logo {{
                width: 80px;
                height: 80px;
                object-fit: contain;
                flex-shrink: 0;
                border: 2px solid red; /* 调试边框 */
            }}
            
            .header-main {{
                flex: 1;
                text-align: center;
                margin: 0 2rem;
            }}
            
            .header-main h1 {{
                font-family: 'Times New Roman', Times, serif;
                font-size: 1.8rem;
                margin: 0 0 0.25rem 0;
                font-weight: bold;
            }}
            
            .header-main p {{
                font-size: 1rem;
                margin: 0;
                font-weight: bold;
                color: #333;
            }}
            
            .debug-info {{
                background: #ffffcc;
                border: 1px solid #cccc00;
                padding: 1rem;
                margin: 1rem 0;
                font-size: 0.9rem;
            }}
        </style>
    </head>
    <body>
        <div class="report-page">
            <header class="report-header">
                <div class="header-top">
                    <!-- 完全复制audio_details.html的logo结构 -->
                    <img src="data:image/png;base64,{logo_base64}" alt="Cognitive Health Logo" class="header-logo">
                    <div class="header-main">
                        <h1>Cognitive Health Speech Analysis</h1>
                        <p>CONFIDENTIAL SCIENTIFIC REPORT</p>
                    </div>
                    <div style="width: 80px;"></div> <!-- 占位符保持居中 -->
                </div>
                
                <div class="debug-info">
                    <h3>Logo测试信息：</h3>
                    <ul>
                        <li><strong>Logo Base64长度：</strong> {len(logo_base64)}</li>
                        <li><strong>Logo状态：</strong> {'✅ 已加载' if logo_base64 else '❌ 未加载'}</li>
                        <li><strong>测试时间：</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                        <li><strong>HTML结构：</strong> 完全复制audio_details.html</li>
                        <li><strong>CSS样式：</strong> 完全复制audio_details.html</li>
                        <li><strong>调试边框：</strong> 红色边框标识logo区域</li>
                    </ul>
                    
                    {f'<p><strong>Base64前缀：</strong> {logo_base64[:100]}...</p>' if logo_base64 else '<p><strong>Base64：</strong> 空值</p>'}
                </div>
            </header>
            
            <main style="padding: 2rem;">
                <h2>简化Logo测试结果</h2>
                <p>如果logo正确显示，您应该在页面左上角看到logo图片（带红色调试边框）。</p>
                
                <h3>修复内容：</h3>
                <ol>
                    <li>✅ 简化了get_logo_base64()函数</li>
                    <li>✅ 移除了复杂的路径检测逻辑</li>
                    <li>✅ 直接使用已知的工作路径</li>
                    <li>✅ 简化了HTML结构，移除了链接包装</li>
                    <li>✅ 完全复制audio_details.html的logo实现</li>
                    <li>✅ 在模板生成前预先获取logo数据</li>
                </ol>
            </main>
        </div>
    </body>
    </html>"""
    
    # 生成PDF
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html_template.encode("UTF-8")), result)
    
    if not pdf.err:
        pdf_bytes = result.getvalue()
        
        # 保存PDF文件
        output_filename = 'test_logo_simplified.pdf'
        with open(output_filename, 'wb') as f:
            f.write(pdf_bytes)
        
        print(f"✅ 简化logo测试PDF生成成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print(f"📁 文件保存为: {output_filename}")
        print(f"🖼️  Logo状态: {'✅ 应该显示' if logo_base64 else '❌ 不会显示'}")
        
        return True
    else:
        print(f"❌ PDF生成失败: {pdf.err}")
        return False

def test_complete_pdf():
    """测试完整的PDF生成"""
    if not DJANGO_AVAILABLE:
        print("⚠️  Django环境不可用，跳过完整PDF测试")
        return False
    
    print("\n🔄 测试完整PDF生成...")
    
    # 测试数据
    test_data = {
        'filename': 'logo_simplified_test.wav',
        'age': '70',
        'relationship': 'my_self',
        'occupation': 'retired'
    }
    
    test_result = {
        'Predicted mmse score': 24.8,
        'Percentile': 76,
        'Model': 'Logo Simplified Test Model'
    }
    
    try:
        # 先测试logo函数
        logo_base64 = test_django_logo_function()
        
        # 生成完整PDF
        pdf_bytes = generate_pdf_with_xhtml2pdf(test_data, test_result)
        
        # 保存PDF文件
        output_filename = 'test_logo_simplified_complete.pdf'
        with open(output_filename, 'wb') as f:
            f.write(pdf_bytes)
        
        print(f"✅ 完整PDF生成成功!")
        print(f"📄 文件大小: {len(pdf_bytes)} bytes")
        print(f"📁 文件保存为: {output_filename}")
        print(f"🖼️  Logo状态: {'✅ 应该显示' if logo_base64 else '❌ 不会显示'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整PDF生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🔍 开始测试简化后的logo功能...")
    
    # 测试1：简单logo测试
    success1 = create_simple_logo_test()
    
    # 测试2：完整PDF测试
    success2 = test_complete_pdf()
    
    if success1 or success2:
        print("\n🎉 Logo功能测试完成!")
        print("\n📋 简化修复总结:")
        print("✅ 1. 简化了get_logo_base64()函数")
        print("✅ 2. 移除了复杂的路径检测")
        print("✅ 3. 直接使用已知工作路径")
        print("✅ 4. 简化了HTML结构")
        print("✅ 5. 完全复制audio_details.html的实现")
        print("✅ 6. 预先获取logo数据")
        print("\n💡 如果logo仍然不显示，请检查:")
        print("- PDF阅读器是否支持内嵌图片")
        print("- Base64编码是否正确")
        print("- 图片格式是否兼容")
    else:
        print("\n❌ 所有测试失败，请检查错误信息")
