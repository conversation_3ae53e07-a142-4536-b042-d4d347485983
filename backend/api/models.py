from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.contrib.auth.base_user import BaseUserManager
from django.utils import timezone
from django.core.validators import validate_email
import uuid
import os
import hashlib
import random
import string
from datetime import datetime, timed<PERSON><PERSON>


def generate_default_names():
    """
    生成默认的名字和姓氏（随机选择）
    """
    import random

    # 默认姓氏列表（常见中文姓氏）
    default_surnames = [
        '张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴',
        '徐', '孙', '马', '朱', '胡', '林', '郭', '何', '高', '罗',
        '郑', '梁', '谢', '宋', '唐', '许', '邓', '冯', '韩', '曹'
    ]

    # 默认名字列表
    default_first_names = [
        '小明', '小红', '小华', '小丽', '小强', '小芳', '小军', '小燕',
        '小伟', '小敏', '小杰', '小娟', '小峰', '小霞', '小宇', '小琳',
        '小涛', '小雪', '小龙', '小梅', '小鹏', '小玲', '小辉', '小慧',
        '小磊', '小静', '小飞', '小兰', '小刚', '小萍'
    ]

    # 随机选择姓氏和名字
    surname = random.choice(default_surnames)
    first_name = random.choice(default_first_names)

    return first_name, surname


# username生成函数已删除，因为不再使用username字段


# generate_unique_username函数已删除，因为不再使用username字段


class CustomUserManager(BaseUserManager):
    """
    自定义用户管理器，使用邮箱作为唯一标识符
    """
    def create_user(self, email, password=None, **extra_fields):
        """
        创建并保存普通用户
        """
        if not email:
            raise ValueError('邮箱地址是必需的')

        email = self.normalize_email(email)
        validate_email(email)

        # user_id字段已删除，不再需要生成

        # 处理名字和姓氏，如果为空则生成默认值
        first_name = extra_fields.get('first_name', '').strip()
        last_name = extra_fields.get('last_name', '').strip()

        if not first_name or not last_name:
            # 生成默认名字和姓氏
            default_first_name, default_last_name = generate_default_names()

            if not first_name:
                extra_fields['first_name'] = default_first_name

            if not last_name:
                extra_fields['last_name'] = default_last_name

        # username字段已删除，不再生成username

        # 确保普通用户默认为未激活状态（除非明确指定为True）
        if 'is_active' not in extra_fields:
            extra_fields['is_active'] = False

        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        创建并保存超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    """
    Custom User Model - Uses email as primary authentication field
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True, verbose_name='Email Address')
    first_name = models.CharField(max_length=30, blank=True, verbose_name='First Name')
    last_name = models.CharField(max_length=30, blank=True, verbose_name='Last Name')

    is_active = models.BooleanField(default=False, verbose_name='Is Active')
    is_staff = models.BooleanField(default=False, verbose_name='Is Staff')
    is_superuser = models.BooleanField(default=False, verbose_name='Is Superuser')

    date_joined = models.DateTimeField(default=timezone.now, verbose_name='Date Joined')
    last_login = models.DateTimeField(null=True, blank=True, verbose_name='Last Login')

    # Avatar field
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True, verbose_name='Avatar')

    # User guidelines and privacy agreement status
    has_agreed_to_terms = models.BooleanField(default=False, verbose_name='Has Agreed to Terms')
    terms_agreed_at = models.DateTimeField(null=True, blank=True, verbose_name='Terms Agreed At')

    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        db_table = 'api_customuser'

    def __str__(self):
        return self.email

    @property
    def full_name(self):
        """Returns user full name (first_name last_name)"""
        # Always has first and last name (either user input or system generated defaults)
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def display_name(self):
        """Returns display name"""
        # Use full name directly since we always have first and last name
        return self.full_name

    def _looks_like_real_name(self, name):
        """Determines if string looks like a real name"""
        # If contains Chinese characters, might be a real name
        if any('\u4e00' <= char <= '\u9fff' for char in name):
            return True

        # If composed of letters and reasonable length, might be a real name
        if name.isalpha() and 2 <= len(name) <= 20:
            return True

        # If contains common name separators
        if any(sep in name for sep in ['.', '_']) and len(name) >= 3:
            return True

        return False

    @property
    def first_name_display(self):
        """Returns display first name"""
        if self.first_name:
            return self.first_name

        # Try to infer from email or other information
        email_username = self.email.split('@')[0]
        if '.' in email_username:
            parts = email_username.split('.')
            if len(parts) >= 2 and parts[0].isalpha():
                return parts[0].capitalize()

        return "Not Set"

    @property
    def last_name_display(self):
        """返回显示用的姓氏"""
        if self.last_name:
            return self.last_name

        # 尝试从邮箱推断
        email_username = self.email.split('@')[0]
        if '.' in email_username:
            parts = email_username.split('.')
            if len(parts) >= 2 and parts[1].isalpha():
                return parts[1].capitalize()

        return "未设置"


def user_upload_path(instance, filename):
    """
    Generate user upload file path
    Create folder based on user email
    File naming format: {original_name}_{upload_time_DDMMYYYY}_{short_hash}.{extension}
    """
    user_email = instance.user.email if instance.user else 'anonymous'

    # Clean email address for use as folder name
    # Replace @ and . with _ to ensure safe folder name
    safe_email = user_email.replace('@', '_').replace('.', '_')

    # Separate original filename and extension
    base, ext = os.path.splitext(filename)
    now = datetime.now()

    # Upload time format: DDMMYYYY
    date_str = now.strftime('%d%m%Y')

    # Generate short hash to ensure uniqueness
    # Use user email, original filename, precise timestamp (including microseconds) to generate hash
    hash_input = f"{user_email}_{base}_{now.strftime('%Y%m%d_%H%M%S_%f')}"
    hash_str = hashlib.md5(hash_input.encode()).hexdigest()[:8]  # 8-character short hash

    # New filename format: {original_name}_{upload_time_DDMMYYYY}_{short_hash}.{extension}
    new_filename = f"{base}_{date_str}_{hash_str}{ext}"

    # Folder format: user email (safely processed)
    return os.path.join(safe_email, new_filename)

class EmailVerificationToken(models.Model):
    """
    Email Verification Token Model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='verification_tokens')
    token = models.CharField(max_length=255, unique=True)
    verification_code = models.CharField(max_length=6, verbose_name='Verification Code', null=True, blank=True)  # 6-digit verification code
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Email Verification Token'
        verbose_name_plural = 'Email Verification Tokens'

    def __str__(self):
        return f"{self.user.email} - Verification Token"

    def is_expired(self):
        """Check if token is expired"""
        return timezone.now() > self.expires_at

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)


class PasswordResetToken(models.Model):
    """
    密码重置令牌模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='password_reset_tokens')
    token = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)

    class Meta:
        verbose_name = '密码重置令牌'
        verbose_name_plural = '密码重置令牌'

    def __str__(self):
        return f"{self.user.email} - 密码重置令牌"

    def is_expired(self):
        """检查令牌是否过期"""
        return timezone.now() > self.expires_at

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=1)
        super().save(*args, **kwargs)


class AudioAnalysis(models.Model):
    """
    Audio Analysis Model
    """
    STATUS_CHOICES = [
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='audio_analyses', verbose_name="User")
    filename = models.CharField(max_length=255, verbose_name="Filename")
    audio_file = models.FileField(upload_to=user_upload_path, verbose_name="Audio File Path")
    result = models.TextField(blank=True, null=True, verbose_name="Analysis Result")
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="Upload Time")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='processing',
        verbose_name="Analysis Status"
    )

    # User information fields
    relationship = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="Audio Speaker",
        help_text="Relationship to speaker (e.g., Myself, My Father, My Mother)"
    )
    occupation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="Occupation",
        help_text="Speaker's occupation"
    )
    age = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name="Age",
        help_text="Speaker's age"
    )
    date_of_birth = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name="Date of Birth",
        help_text="Speaker's date of birth (DD-MM-YYYY format)"
    )

    class Meta:
        verbose_name = 'Audio Analysis'
        verbose_name_plural = 'Audio Analyses'
        ordering = ['-upload_time']

    def __str__(self):
        return f"{self.user.email} - {self.filename} - {self.status}"

