# Generated by Django 5.2.3 on 2025-07-22 08:03

import api.models
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="邮箱地址"
                    ),
                ),
                (
                    "first_name",
                    models.CharField(blank=True, max_length=30, verbose_name="名字"),
                ),
                (
                    "last_name",
                    models.Char<PERSON>ield(blank=True, max_length=30, verbose_name="姓氏"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=False, verbose_name="是否激活"),
                ),
                (
                    "is_staff",
                    models.BooleanField(default=False, verbose_name="是否为员工"),
                ),
                (
                    "is_superuser",
                    models.BooleanField(default=False, verbose_name="是否为超级用户"),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="注册时间"
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最后登录时间"
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True, null=True, upload_to="avatars/", verbose_name="头像"
                    ),
                ),
                (
                    "has_agreed_to_terms",
                    models.BooleanField(
                        default=False, verbose_name="是否同意用户须知和免责协议"
                    ),
                ),
                (
                    "terms_agreed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="同意协议时间"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
                "db_table": "api_customuser",
            },
        ),
        migrations.CreateModel(
            name="AudioAnalysis",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("filename", models.CharField(max_length=255, verbose_name="文件名")),
                (
                    "audio_file",
                    models.FileField(
                        upload_to=api.models.user_upload_path,
                        verbose_name="音频文件路径",
                    ),
                ),
                (
                    "result",
                    models.TextField(blank=True, null=True, verbose_name="分析结果"),
                ),
                (
                    "upload_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="上传时间"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="processing",
                        max_length=20,
                        verbose_name="分析状态",
                    ),
                ),
                (
                    "relationship",
                    models.CharField(
                        blank=True,
                        help_text="与说话人的关系 (例如: 我自己, 我的父亲, 我的母亲)",
                        max_length=50,
                        null=True,
                        verbose_name="音频说话人",
                    ),
                ),
                (
                    "occupation",
                    models.CharField(
                        blank=True,
                        help_text="说话人的职业",
                        max_length=100,
                        null=True,
                        verbose_name="职业",
                    ),
                ),
                (
                    "age",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="说话人的年龄",
                        null=True,
                        verbose_name="年龄",
                    ),
                ),
                (
                    "date_of_birth",
                    models.CharField(
                        blank=True,
                        help_text="说话人的出生日期 (DD-MM-YYYY格式)",
                        max_length=20,
                        null=True,
                        verbose_name="出生日期",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audio_analyses",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "音频分析",
                "verbose_name_plural": "音频分析",
                "ordering": ["-upload_time"],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                (
                    "verification_code",
                    models.CharField(
                        blank=True, max_length=6, null=True, verbose_name="验证码"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "邮箱验证令牌",
                "verbose_name_plural": "邮箱验证令牌",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "密码重置令牌",
                "verbose_name_plural": "密码重置令牌",
            },
        ),
    ]
