# Generated migration for Donation model

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_customuser_has_agreed_to_terms_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Donation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.IntegerField(choices=[(50, '$50'), (100, '$100'), (150, '$150'), (200, '$200'), (250, '$250'), (300, '$300'), (350, '$350'), (400, '$400'), (450, '$450'), (500, '$500')], default=150, verbose_name='Donation Amount (USD)')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Payment Status')),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Payment Intent ID')),
                ('stripe_client_secret', models.CharField(blank=True, max_length=255, null=True, verbose_name='Stripe Client Secret')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('audio_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='donations', to='api.audioanalysis', verbose_name='Audio Analysis')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='donations', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Donation',
                'verbose_name_plural': 'Donations',
                'ordering': ['-created_at'],
            },
        ),
    ]
