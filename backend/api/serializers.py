from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser, AudioAnalysis, EmailVerificationToken, PasswordResetToken
from .email_utils import send_verification_email, send_password_reset_email
import secrets


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    User Registration Serializer
    """
    password = serializers.Char<PERSON>ield(write_only=True, min_length=8)
    password_confirm = serializers.Char<PERSON>ield(write_only=True)
    first_name = serializers.CharField(required=False, allow_blank=True, max_length=30)
    last_name = serializers.CharField(required=False, allow_blank=True, max_length=30)

    class Meta:
        model = CustomUser
        fields = ('email', 'password', 'password_confirm', 'first_name', 'last_name')
        # Only need email, password and name information

    def validate_email(self, value):
        """Validate and normalize email address"""
        # Normalize email address (convert to lowercase and remove spaces)
        from django.contrib.auth.models import BaseUserManager

        # Clean input first
        cleaned_email = value.strip().lower()
        # Then use Django's standard normalization
        normalized_email = BaseUserManager.normalize_email(cleaned_email)

        # Don't check uniqueness here, let database handle it
        # This avoids race condition issues
        return normalized_email

    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Password confirmation does not match")

        # Validate password strength
        try:
            validate_password(attrs['password'])
        except ValidationError as e:
            raise serializers.ValidationError({"password": e.messages})

        return attrs

    def create(self, validated_data):
        """Create user and send verification email - user must activate via verification code"""
        from django.db import IntegrityError

        # Remove password confirmation field
        validated_data.pop('password_confirm')

        # Force set user as inactive - this is key!
        validated_data['is_active'] = False

        print(f"🔍 Registration debug - setting is_active = False")

        try:
            # Create user
            user = CustomUser.objects.create_user(**validated_data)

            # Triple ensure user is inactive
            user.is_active = False
            user.save()

            # Confirm again
            user.refresh_from_db()

            print(f"🔍 User creation completed:")
            print(f"   Email: {user.email}")
            print(f"   is_active: {user.is_active}")
            print(f"   ID: {user.id}")

            if user.is_active:
                print(f"❌ Error: User was unexpectedly activated!")
                user.is_active = False
                user.save()
                print(f"✅ Forced to set as inactive")

            # Send verification code email
            send_verification_email(user.email)

            return user

        except IntegrityError as e:
            # Handle database uniqueness constraint errors
            if 'UNIQUE constraint failed' in str(e) and 'email' in str(e):
                raise serializers.ValidationError({"email": ["This email is already registered"]})
            else:
                # Other database errors
                raise serializers.ValidationError({"non_field_errors": ["Registration failed, please try again later"]})


class UserLoginSerializer(serializers.Serializer):
    """
    User Login Serializer
    """
    email = serializers.EmailField()
    password = serializers.CharField()

    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            # Check if user exists
            try:
                user = CustomUser.objects.get(email=email)
            except CustomUser.DoesNotExist:
                raise serializers.ValidationError("Invalid email or password")

            # Validate password first
            if not user.check_password(password):
                raise serializers.ValidationError("Invalid email or password")

            # Refresh user data to ensure latest status
            user.refresh_from_db()

            # Debug information
            print(f"🔍 Login validation debug:")
            print(f"   Email: {user.email}")
            print(f"   is_active: {user.is_active}")
            print(f"   ID: {user.id}")

            # Force check user activation status - this is key!
            if not user.is_active:
                print(f"❌ User {email} not activated, denying login")
                raise serializers.ValidationError("Account not activated. Please enter verification code to activate your account")

            print(f"✅ User {email} is activated, allowing login")
            attrs['user'] = user
        else:
            raise serializers.ValidationError("Email and password are required")

        return attrs


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Password Reset Request Serializer
    """
    email = serializers.EmailField()

    def validate_email(self, value):
        """Validate if email exists"""
        try:
            user = CustomUser.objects.get(email=value)
            if not user.is_active:
                raise serializers.ValidationError("Account not activated, cannot reset password")
        except CustomUser.DoesNotExist:
            raise serializers.ValidationError("This email is not registered")
        return value

    def save(self):
        """Send password reset email"""
        email = self.validated_data['email']
        user = CustomUser.objects.get(email=email)

        # Create reset token
        token = secrets.token_urlsafe(32)
        PasswordResetToken.objects.create(user=user, token=token)

        # Send reset email
        send_password_reset_email(user.email, token)

        return user


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Password Reset Confirmation Serializer
    """
    token = serializers.CharField()
    password = serializers.CharField(min_length=8)
    password_confirm = serializers.CharField()

    def validate(self, attrs):
        """Validate token and password"""
        token = attrs.get('token')
        password = attrs.get('password')
        password_confirm = attrs.get('password_confirm')

        # Validate password confirmation
        if password != password_confirm:
            raise serializers.ValidationError("Password confirmation does not match")

        # Validate password strength
        try:
            validate_password(password)
        except ValidationError as e:
            raise serializers.ValidationError({"password": e.messages})

        # Validate token
        try:
            reset_token = PasswordResetToken.objects.get(token=token, is_used=False)
            if reset_token.is_expired():
                raise serializers.ValidationError("Reset link has expired")
            attrs['reset_token'] = reset_token
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError("Invalid reset link")

        return attrs

    def save(self):
        """Reset password"""
        reset_token = self.validated_data['reset_token']
        password = self.validated_data['password']

        user = reset_token.user
        user.set_password(password)
        user.save()

        # Mark token as used
        reset_token.is_used = True
        reset_token.save()

        return user


class UserProfileSerializer(serializers.ModelSerializer):
    """
    User Profile Serializer
    """
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()

    class Meta:
        model = CustomUser
        fields = ('id', 'email', 'first_name', 'last_name',
                 'full_name', 'display_name', 'first_name_display', 'last_name_display',
                 'avatar', 'date_joined', 'last_login')
        read_only_fields = ('id', 'email', 'full_name',
                           'display_name', 'first_name_display', 'last_name_display',
                           'date_joined', 'last_login')


class AudioAnalysisSerializer(serializers.ModelSerializer):
    """
    Audio Analysis Serializer
    """
    user_email = serializers.CharField(source='user.email', read_only=True)

    class Meta:
        model = AudioAnalysis
        fields = [
            'id', 'filename', 'audio_file', 'result', 'upload_time', 'status',
            'relationship', 'occupation', 'age', 'date_of_birth', 'user_email'
        ]
        read_only_fields = ('id', 'upload_time', 'user_email')

    def create(self, validated_data):
        """自动设置用户字段"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)