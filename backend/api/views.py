from rest_framework import status, serializers
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormPars<PERSON>
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import authenticate
from django.shortcuts import redirect
from django.http import HttpResponse
from django.utils import timezone
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from .models import CustomUser, AudioAnalysis, EmailVerificationToken, PasswordResetToken
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    AudioAnalysisSerializer, PasswordResetRequestSerializer, PasswordResetConfirmSerializer
)
from .email_utils import send_welcome_email
from api.tasks import mmse_predict


@method_decorator(csrf_exempt, name='dispatch')
class UserRegistrationView(APIView):
    """
    User Registration View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                return Response({
                    'success': True,
                    'message': 'Registration successful! A verification code has been sent to your email. Please check your inbox and enter the verification code to activate your account.',
                    'email': user.email,
                    'redirect_to': 'verify-code'  # Indicates frontend should redirect to verification code page
                }, status=status.HTTP_201_CREATED)

            except serializers.ValidationError as e:
                # Serializer validation errors (including duplicate email)
                print(f"Registration validation error: {e}")
                return Response({
                    'success': False,
                    'message': 'Registration failed',
                    'errors': e.detail if hasattr(e, 'detail') else {'non_field_errors': [str(e)]}
                }, status=status.HTTP_400_BAD_REQUEST)

            except Exception as e:
                error_msg = str(e)
                print(f"Registration unexpected error: {error_msg}")  # Log error to console

                # Handle database lock errors
                if 'database is locked' in error_msg:
                    return Response({
                        'success': False,
                        'message': 'System is busy, please try again later.',
                        'errors': {'non_field_errors': ['System is busy, please try again later']}
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

                # Other unexpected errors
                else:
                    return Response({
                        'success': False,
                        'message': 'Registration failed, please try again later.',
                        'errors': {'non_field_errors': ['Internal server error, please try again later']}
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Serializer validation failed
        return Response({
            'success': False,
            'message': 'Registration failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class UserLoginView(APIView):
    """
    User Login View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # Double-check that user is activated
            if not user.is_active:
                return Response({
                    'success': False,
                    'message': 'Account not activated. Please enter verification code to activate your account'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Update last login time
            user.last_login = timezone.now()
            user.save()

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response({
                'success': True,
                'message': 'Login successful',
                'data': {
                    'user': {
                        'id': str(user.id),
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'display_name': user.display_name,
                    },
                    'tokens': {
                        'access': str(access_token),
                        'refresh': str(refresh)
                    }
                }
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': 'Login failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserLogoutView(APIView):
    """
    User Logout View
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'success': True,
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Logout failed',
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class VerifyCodeView(APIView):
    """
    Verification Code Validation View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        verification_code = request.data.get('verification_code')

        if not email or not verification_code:
            return Response({
                'success': False,
                'message': 'Please provide email and verification code'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 查找验证令牌 - 兼容新旧数据库结构
            try:
                # 尝试使用verification_code字段
                verification_token = EmailVerificationToken.objects.get(
                    user__email=email,
                    verification_code=verification_code,
                    is_used=False
                )
            except Exception:
                # 如果verification_code字段不存在，尝试匹配token的前6位
                tokens = EmailVerificationToken.objects.filter(
                    user__email=email,
                    is_used=False
                )
                verification_token = None
                for token in tokens:
                    if token.token[:6].upper() == verification_code.upper():
                        verification_token = token
                        break

                if not verification_token:
                    raise EmailVerificationToken.DoesNotExist("Invalid verification code")

            if verification_token.is_expired():
                return Response({
                    'success': False,
                    'message': 'Verification code has expired, please request a new one'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Activate user
            user = verification_token.user
            user.is_active = True
            user.save()

            # Mark token as used
            verification_token.is_used = True
            verification_token.save()

            # Send welcome email
            send_welcome_email(user.email)

            return Response({
                'success': True,
                'message': 'Account activated successfully! You can now log in.'
            }, status=status.HTTP_200_OK)

        except EmailVerificationToken.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Invalid or already used verification code'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Verification failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EmailVerificationView(APIView):
    """
    邮箱验证视图（保留兼容性）
    """
    permission_classes = [AllowAny]

    def get(self, request):
        token = request.GET.get('token')
        if not token:
            return HttpResponse('缺少验证令牌', status=400)

        try:
            verification_token = EmailVerificationToken.objects.get(
                token=token,
                is_used=False
            )

            if verification_token.is_expired():
                return HttpResponse('验证链接已过期', status=400)

            # 激活用户
            user = verification_token.user
            user.is_active = True
            user.save()

            # 标记令牌为已使用
            verification_token.is_used = True
            verification_token.save()

            # 发送欢迎邮件
            send_welcome_email(user.email)

            # 重定向到前端登录页面
            return redirect(f'{settings.LOCAL_BASE_URL}/login?message=账户激活成功，请登录')

        except EmailVerificationToken.DoesNotExist:
            return HttpResponse('无效的验证链接', status=400)
        except Exception as e:
            return HttpResponse(f'验证失败: {str(e)}', status=500)

class ResendVerificationView(APIView):
    """
    重新发送验证码视图
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({
                'success': False,
                'message': '请提供邮箱地址'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(email=email)
            if user.is_active:
                return Response({
                    'success': False,
                    'message': '账户已激活，无需重新发送验证码'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 删除旧的验证令牌
            EmailVerificationToken.objects.filter(user=user, is_used=False).delete()

            # 发送新的验证码
            from .email_utils import send_verification_email
            send_verification_email(email)

            return Response({
                'success': True,
                'message': '验证码已重新发送到您的邮箱，请查收'
            }, status=status.HTTP_200_OK)

        except CustomUser.DoesNotExist:
            return Response({
                'success': False,
                'message': '该邮箱未注册'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'发送失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserProfileView(APIView):
    """
    用户资料视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户资料"""
        user = request.user

        print(f"🔍 UserProfileView GET request:")
        print(f"   User: {user.email}")
        print(f"   User ID: {user.id}")
        print(f"   Is authenticated: {user.is_authenticated}")

        try:
            # 获取用户统计数据
            from .models import AudioAnalysis
            total_analyses = AudioAnalysis.objects.filter(user=user).count()
            successful_analyses = AudioAnalysis.objects.filter(user=user, status='completed').count()
            pending_analyses = AudioAnalysis.objects.filter(user=user, status='processing').count()

            response_data = {
                'success': True,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined.isoformat() if user.date_joined else None,
                    'avatar': user.avatar.url if user.avatar else None,
                },
                'stats': {
                    'total': total_analyses,
                    'successful': successful_analyses,
                    'pending': pending_analyses,
                }
            }

            print(f"✅ Returning user profile data:")
            print(f"   Email: {response_data['user']['email']}")
            print(f"   Name: {response_data['user']['first_name']} {response_data['user']['last_name']}")

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"❌ Error in UserProfileView: {str(e)}")
            return Response({
                'success': False,
                'message': f'Failed to load user profile: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """更新用户资料"""
        user = request.user

        print(f"🔍 UserProfileView PUT request:")
        print(f"   User: {user.email}")
        print(f"   Data: {request.data}")

        try:
            # 更新基本信息
            if 'first_name' in request.data:
                user.first_name = request.data.get('first_name', user.first_name)
            if 'last_name' in request.data:
                user.last_name = request.data.get('last_name', user.last_name)

            # 处理头像上传
            if 'avatar' in request.FILES:
                user.avatar = request.FILES['avatar']

            user.save()

            response_data = {
                'success': True,
                'message': '资料更新成功',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined.isoformat() if user.date_joined else None,
                    'avatar': user.avatar.url if user.avatar else None,
                }
            }

            print(f"✅ User profile updated successfully")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"❌ Error updating user profile: {str(e)}")
            return Response({
                'success': False,
                'message': f'Failed to update profile: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChangePasswordView(APIView):
    """
    修改密码视图
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user

        print(f"🔍 ChangePasswordView POST request:")
        print(f"   User: {user.email}")
        print(f"   Request data: {request.data}")

        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        print(f"   Current password provided: {bool(current_password)}")
        print(f"   New password provided: {bool(new_password)}")

        if not current_password or not new_password:
            print(f"❌ Missing password fields")
            return Response({
                'success': False,
                'message': '请提供当前密码和新密码'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证当前密码
        if not user.check_password(current_password):
            print(f"❌ Current password verification failed")
            return Response({
                'success': False,
                'message': '当前密码不正确'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证新密码强度
        if len(new_password) < 8:
            return Response({
                'success': False,
                'message': '新密码长度至少为8位'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 更新密码
        user.set_password(new_password)
        user.save()

        print(f"✅ Password changed successfully for user: {user.email}")

        return Response({
            'success': True,
            'message': '密码修改成功'
        }, status=status.HTTP_200_OK)


class PasswordResetRequestView(APIView):
    """
    密码重置请求视图
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': '密码重置邮件已发送，请检查您的邮箱'
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': '请求失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(APIView):
    """
    密码重置确认视图
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': '密码重置成功，请使用新密码登录'
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': '重置失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class CheckEmailView(APIView):
    """
    检查邮箱状态的视图 - 支持两段式登录
    """
    permission_classes = [AllowAny]

    def get(self, request):
        email = request.GET.get('email')
        if not email:
            return Response({
                'success': False,
                'message': '请提供邮箱地址'
            }, status=status.HTTP_400_BAD_REQUEST)

        exists = CustomUser.objects.filter(email=email).exists()
        return Response({
            'success': True,
            'exists': exists,
            'message': '邮箱已注册' if exists else '邮箱可用'
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """检查邮箱状态用于两段式登录"""
        email = request.data.get('email')
        if not email:
            return Response({
                'success': False,
                'message': '请提供邮箱地址'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(email=email)

            # 检查用户激活状态
            if not user.is_active:
                return Response({
                    'success': True,
                    'exists': True,
                    'is_active': False,
                    'message': '账户未激活，请先激活账户',
                    'action': 'activate'  # 前端根据此字段决定显示激活选项
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': True,
                    'exists': True,
                    'is_active': True,
                    'message': '请输入密码',
                    'action': 'login'  # 前端根据此字段显示密码输入
                }, status=status.HTTP_200_OK)

        except CustomUser.DoesNotExist:
            return Response({
                'success': True,
                'exists': False,
                'is_active': False,
                'message': '邮箱未注册，请先注册',
                'action': 'register'  # 前端根据此字段显示注册选项
            }, status=status.HTTP_200_OK)


class AudioUploadView(APIView):
    """
    音频上传视图
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        serializer = AudioAnalysisSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            instance = serializer.save()
            audio_path = instance.audio_file.path

            # 调试信息
            print(f"🔍 音频上传成功:")
            print(f"   文件ID: {instance.id}")
            print(f"   文件路径: {audio_path}")
            print(f"   用户: {instance.user.email}")

            # 启动Celery任务进行分析
            try:
                task = mmse_predict.delay(instance.id, audio_path)
                print(f"✅ Celery任务已启动: {task.id}")
            except Exception as e:
                print(f"❌ Celery任务启动失败: {str(e)}")
                instance.status = "failed"

            instance.save()
            return Response({
                "success": True,
                "message": "音频上传成功，正在分析中！",
                "data": AudioAnalysisSerializer(instance).data
            }, status=status.HTTP_201_CREATED)
        return Response({
            "success": False,
            "message": "上传失败",
            "errors": serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class AudioHistoryView(ListAPIView):
    """
    音频分析历史视图
    """
    serializer_class = AudioAnalysisSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return AudioAnalysis.objects.filter(user=self.request.user).order_by('-upload_time')

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'count': queryset.count()
        }, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class HealthCheckView(APIView):
    """
    健康检查视图
    """
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'success': True,
            'message': 'API服务正常运行',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)


class UserTermsAgreementView(APIView):
    """
    User Guidelines and Privacy Agreement Management
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user agreement status"""
        user = request.user

        print(f"🔍 UserTermsAgreementView GET request:")
        print(f"   User: {user.email}")
        print(f"   Has agreed to terms: {user.has_agreed_to_terms}")
        print(f"   Terms agreed at: {user.terms_agreed_at}")

        return Response({
            'success': True,
            'data': {
                'has_agreed_to_terms': user.has_agreed_to_terms,
                'terms_agreed_at': user.terms_agreed_at.isoformat() if user.terms_agreed_at else None
            }
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """User agrees to terms"""
        user = request.user
        agreed = request.data.get('agreed', False)

        print(f"🔍 UserTermsAgreementView POST request:")
        print(f"   User: {user.email}")
        print(f"   Agreed: {agreed}")

        if agreed:
            user.has_agreed_to_terms = True
            user.terms_agreed_at = timezone.now()
            user.save()

            print(f"✅ User {user.email} agreed to terms at {user.terms_agreed_at}")

            return Response({
                'success': True,
                'message': 'Your agreement status has been successfully recorded',
                'data': {
                    'has_agreed_to_terms': user.has_agreed_to_terms,
                    'terms_agreed_at': user.terms_agreed_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        else:
            print(f"❌ User {user.email} declined terms")
            return Response({
                'success': False,
                'message': 'You need to agree to the user guidelines and privacy policy to use the audio upload feature'
            }, status=status.HTTP_400_BAD_REQUEST)
