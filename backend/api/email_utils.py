from django.conf import settings
from django.core.mail import send_mail
from django.utils import timezone
from datetime import timedelta
import secrets

# 从后端settings导入URL配置
API_BASE_URL = settings.API_BASE_URL
LOCAL_BASE_URL = settings.LOCAL_BASE_URL


def get_name_from_email(email):
    """从邮箱提取用户名"""
    return email.split('@')[0].replace('.', ' ').strip().capitalize()


def create_verification_token():
    """创建验证令牌"""
    return secrets.token_urlsafe(32)


def generate_verification_code():
    """生成6位数字验证码"""
    import random
    return str(random.randint(100000, 999999))


def create_password_reset_token():
    """创建密码重置令牌"""
    return secrets.token_urlsafe(32)


def send_verification_email(email):
    """发送邮箱验证邮件"""
    try:
        from .models import EmailVerificationToken, CustomUser
        
        # 删除该用户之前的验证令牌
        EmailVerificationToken.objects.filter(user__email=email).delete()

        user = CustomUser.objects.get(email=email)
        token = create_verification_token()
        verification_code = generate_verification_code()

        # 调试信息
        print(f"🔍 邮件发送调试信息:")
        print(f"   邮箱: {email}")
        print(f"   生成的验证码: {verification_code}")
        print(f"   验证码类型: {type(verification_code)}")

        # 创建验证令牌记录
        try:
            verification_token = EmailVerificationToken.objects.create(
                user=user,
                token=token,
                verification_code=verification_code,
                expires_at=timezone.now() + timedelta(hours=24)
            )
        except Exception as e:
            # 如果verification_code字段不存在，使用旧的方式创建
            print(f"⚠️  数据库字段不存在，使用兼容模式: {e}")
            verification_token = EmailVerificationToken.objects.create(
                user=user,
                token=token,
                expires_at=timezone.now() + timedelta(hours=24)
            )
            # 手动设置验证码（如果字段存在）
            try:
                verification_token.verification_code = verification_code
                verification_token.save()
            except:
                print("⚠️  无法保存验证码到数据库，使用token作为验证码")

        # 验证保存的验证码
        try:
            saved_code = getattr(verification_token, 'verification_code', None)
            print(f"   保存到数据库的验证码: {saved_code}")
            if not saved_code:
                # 如果无法保存验证码，使用token的前6位作为验证码
                verification_code = token[:6].upper()
                print(f"   使用token前6位作为验证码: {verification_code}")
        except:
            # 如果字段不存在，使用token的前6位
            verification_code = token[:6].upper()
            print(f"   字段不存在，使用token前6位: {verification_code}")

        name = get_name_from_email(email)

        # 强制验证验证码值
        print(f"🔍 邮件模板构建前验证:")
        print(f"   name: {name}")
        print(f"   verification_code: {verification_code}")
        print(f"   verification_code type: {type(verification_code)}")

        # 邮件主题
        subject = "🧠 您的账户激活验证码"
        
        # HTML邮件内容
        html_message = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>激活您的账户</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #e9ecef;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .header p {{
                    color: #6c757d;
                    margin: 5px 0 0 0;
                    font-size: 16px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .content h2 {{
                    color: #2c3e50;
                    margin-bottom: 20px;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                    transition: transform 0.2s;
                }}
                .button:hover {{
                    transform: translateY(-2px);
                }}
                .link-box {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    border-left: 4px solid #667eea;
                    margin: 20px 0;
                    word-break: break-all;
                    font-family: monospace;
                    font-size: 14px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
                .warning {{
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧠 认知健康</h1>
                    <p>AI驱动的语音分析平台</p>
                </div>
                <div class="content">
                    <h2>欢迎，{name}！</h2>
                    <p>感谢您注册<strong>认知健康语音分析</strong>平台！</p>
                    <p>为了完成注册并激活您的账户，请使用以下验证码：</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 10px; letter-spacing: 8px; display: inline-block;">
                            {verification_code}
                        </div>
                    </div>

                    <p style="text-align: center; color: #666; font-size: 14px;">请在激活页面输入上述6位数字验证码</p>
                    
                    <div class="warning">
                        <p><strong>⚠️ 重要提醒：</strong></p>
                        <ul>
                            <li>此验证码将在24小时后过期</li>
                            <li>如果您没有注册此账户，请忽略此邮件</li>
                            <li>为了您的安全，请不要将此验证码分享给他人</li>
                        </ul>
                    </div>
                    
                    <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>🔒 您的隐私很重要：</strong></p>
                        <p>您的所有数据都经过加密并安全存储。我们遵循严格的医疗隐私标准来保护您的信息。</p>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>认知健康团队</strong></p>
                    <p>技术支持：<strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """.format(name=name, verification_code=verification_code)
        
        # 纯文本邮件内容
        text_message = f"""
        欢迎使用认知健康，{name}！

        感谢您注册我们的AI驱动语音分析平台。

        为了完成注册并激活您的账户，请使用以下验证码：

        验证码：{verification_code}

        请在激活页面输入上述6位数字验证码。

        重要提醒：此验证码将在24小时后过期。

        如果您没有注册此账户，请忽略此邮件。

        最好的祝愿，
        认知健康团队

        技术支持：<EMAIL>
        """
        
        # 发送邮件前的调试信息
        print("=" * 80)
        print(f"📧 正在发送验证邮件到: {email}")
        print(f"🔢 验证码: {verification_code}")
        print(f"📝 邮件主题: {subject}")
        print("📄 HTML邮件内容预览:")
        print(html_message[:500] + "..." if len(html_message) > 500 else html_message)
        print("📄 文本邮件内容预览:")
        print(text_message[:300] + "..." if len(text_message) > 300 else text_message)
        print("=" * 80)

        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )

        print("=" * 80)
        print(f"✅ 验证邮件已发送到 {email}")
        print(f"🔢 验证码: {verification_code}")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 发送验证邮件失败 {email}: {e}")
        return False


def send_password_reset_email(email, token):
    """发送密码重置邮件"""
    try:
        name = get_name_from_email(email)
        
        # 构建重置链接 - 指向前端页面
        reset_link = f"{LOCAL_BASE_URL}/password-reset-confirm/?token={token}"
        
        # 邮件主题
        subject = "🔐 重置您的密码"
        
        # HTML邮件内容
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>重置密码</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #e9ecef;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                }}
                .link-box {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    border-left: 4px solid #ff6b6b;
                    margin: 20px 0;
                    word-break: break-all;
                    font-family: monospace;
                    font-size: 14px;
                }}
                .warning {{
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧠 认知健康</h1>
                </div>
                <div class="content">
                    <h2>密码重置请求</h2>
                    <p>您好，{name}！</p>
                    <p>我们收到了重置您账户密码的请求。</p>
                    <p>如果这是您发起的请求，请点击下面的按钮重置密码：</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_link}" class="button">🔐 重置密码</a>
                    </div>
                    
                    <p>或者复制并粘贴此链接到您的浏览器中：</p>
                    <div class="link-box">
                        {reset_link}
                    </div>
                    
                    <div class="warning">
                        <p><strong>⚠️ 安全提醒：</strong></p>
                        <ul>
                            <li>此重置链接将在1小时后过期</li>
                            <li>如果您没有请求重置密码，请忽略此邮件</li>
                            <li>为了您的安全，请不要将此链接分享给他人</li>
                        </ul>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>认知健康团队</strong></p>
                    <p>技术支持：<strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 纯文本邮件内容
        text_message = f"""
        密码重置请求

        您好，{name}！

        我们收到了重置您账户密码的请求。

        如果这是您发起的请求，请点击下面的链接重置密码：
        {reset_link}

        重要提醒：此重置链接将在1小时后过期。

        如果您没有请求重置密码，请忽略此邮件。

        最好的祝愿，
        认知健康团队

        技术支持：<EMAIL>
        """
        
        # 发送邮件
        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )
        
        print(f"✅ 密码重置邮件已发送到 {email}")
        return True
        
    except Exception as e:
        print(f"❌ 发送密码重置邮件失败 {email}: {e}")
        return False


def send_welcome_email(email):
    """发送欢迎邮件（账户激活后）"""
    try:
        name = get_name_from_email(email)
        
        # 邮件主题
        subject = "🎉 欢迎来到认知健康！"
        
        # HTML邮件内容
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>欢迎</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 欢迎，{name}！</h1>
                </div>
                <div class="content">
                    <p>您的账户已成功激活！</p>
                    <p>现在您可以登录并开始使用我们的AI驱动语音分析平台。</p>
                    
                    <div style="text-align: center;">
                        <a href="{LOCAL_BASE_URL}/login" class="button">🚀 开始使用</a>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>认知健康团队</strong></p>
                    <p>技术支持：<strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_message = f"""
        欢迎来到认知健康，{name}！

        您的账户已成功激活。
        现在您可以登录并开始使用我们的平台。

        访问：{LOCAL_BASE_URL}/login

        最好的祝愿，
        认知健康团队
        """
        
        print("=" * 80)
        print(f"🎉 正在发送欢迎邮件到: {email}")
        print("=" * 80)

        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )

        print("=" * 80)
        print(f"✅ 欢迎邮件已发送到 {email}")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"❌ 发送欢迎邮件失败 {email}: {e}")
        return False
