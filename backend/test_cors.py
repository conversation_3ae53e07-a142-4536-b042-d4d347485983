#!/usr/bin/env python
"""
测试CORS配置
"""
import requests
import json

def test_cors_preflight():
    """测试CORS预检请求"""
    print("🧪 测试CORS预检请求...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    
    # 发送OPTIONS请求（CORS预检）
    headers = {
        'Origin': 'http://127.0.0.1:8000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
    }
    
    try:
        response = requests.options(api_url, headers=headers)
        print(f"OPTIONS请求状态码: {response.status_code}")
        print(f"响应头:")
        for key, value in response.headers.items():
            if 'cors' in key.lower() or 'access-control' in key.lower():
                print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("✅ CORS预检请求成功")
            return True
        else:
            print("❌ CORS预检请求失败")
            return False
            
    except Exception as e:
        print(f"❌ CORS测试异常: {e}")
        return False

def test_cors_actual_request():
    """测试实际的CORS请求"""
    print("\n🧪 测试实际的CORS请求...")
    
    api_url = "http://127.0.0.1:8001/api/register/"
    
    user_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "CORS",
        "last_name": "Test"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://127.0.0.1:8000'
    }
    
    try:
        response = requests.post(api_url, json=user_data, headers=headers)
        print(f"POST请求状态码: {response.status_code}")
        print(f"响应头:")
        for key, value in response.headers.items():
            if 'cors' in key.lower() or 'access-control' in key.lower():
                print(f"  {key}: {value}")
        
        print(f"响应内容: {response.json()}")
        
        if response.status_code in [200, 201]:
            print("✅ CORS实际请求成功")
            return True
        else:
            print("❌ CORS实际请求失败")
            return False
            
    except Exception as e:
        print(f"❌ CORS实际请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试CORS配置...")
    print("=" * 50)
    
    tests = [
        ("CORS预检请求", test_cors_preflight),
        ("CORS实际请求", test_cors_actual_request),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 CORS配置正常！")
    else:
        print("⚠️  CORS配置有问题，需要修复")

if __name__ == "__main__":
    main()
