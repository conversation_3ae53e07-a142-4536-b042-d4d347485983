#!/usr/bin/env python3
"""
Simple test to verify transparency parameter HTML template generation
"""

def test_transparency_template():
    """Test the HTML template with different transparency values"""
    
    # This is the corrected template from the fix
    template = '<span class="barcode" style="color: #000000; background-color: rgba({},{},{},{}); font-style: italic;">{}</span>'
    
    print("Testing transparency parameter in HTML template...")
    print("=" * 60)
    
    # Test different transparency values
    transparency_values = [0.0, 0.3, 0.5, 0.7, 1.0]
    
    # Mock color values (RGB)
    r, g, b = 100, 150, 200
    word = "test"
    
    for transparency in transparency_values:
        # Convert transparency to alpha (same logic as in colorize function)
        alpha = 1.0 - transparency
        
        # Generate HTML with the template
        html_result = template.format(r, g, b, alpha, word)
        
        print(f"\n🧪 Transparency = {transparency}")
        print(f"   Alpha = {alpha:.2f}")
        print(f"   HTML: {html_result}")
        
        # Verify the alpha value is correctly embedded
        if f"rgba({r},{g},{b},{alpha:.1f}" in html_result:
            print(f"   ✅ Alpha value {alpha:.2f} correctly embedded in HTML")
        else:
            print(f"   ❌ Alpha value not found in HTML")
    
    print("\n" + "=" * 60)
    print("Summary:")
    print("- transparency=0.0 → alpha=1.0 (完全不透明)")
    print("- transparency=0.3 → alpha=0.7 (轻微透明)")
    print("- transparency=0.5 → alpha=0.5 (半透明)")
    print("- transparency=0.7 → alpha=0.3 (较透明)")
    print("- transparency=1.0 → alpha=0.0 (完全透明)")

if __name__ == "__main__":
    test_transparency_template()
