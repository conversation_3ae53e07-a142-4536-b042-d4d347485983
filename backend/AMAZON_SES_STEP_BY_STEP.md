# 📧 Amazon SES 详细配置步骤

## 🎯 完整配置流程概览

```
1. 注册AWS账户 → 2. 访问SES控制台 → 3. 验证发送方身份 → 4. 申请移出沙盒 
→ 5. 创建IAM用户 → 6. 配置Django → 7. 测试发送
```

---

## 步骤1: 注册AWS账户 🔐

### 1.1 访问AWS官网
1. 打开浏览器，访问 [https://aws.amazon.com/](https://aws.amazon.com/)
2. 点击右上角 **"创建AWS账户"** 或 **"Create an AWS Account"**

### 1.2 填写账户信息
```
📧 邮箱地址: <EMAIL>
🔑 密码: 设置强密码
🏢 账户名称: 你的公司或项目名称
```

### 1.3 选择账户类型
- **个人账户**: 适合个人开发者
- **商业账户**: 适合公司使用
- 填写联系信息（姓名、地址、电话）

### 1.4 添加付款方式
- 输入信用卡信息（用于身份验证）
- **注意**: 免费额度内不会收费
- 支持Visa、MasterCard、American Express

### 1.5 身份验证
- 选择电话验证或短信验证
- 输入收到的验证码
- 完成身份验证

### 1.6 选择支持计划
- **基本支持**: 免费（推荐）
- **开发者支持**: $29/月
- **商业支持**: $100/月

### 1.7 完成注册
- 等待账户激活（通常几分钟）
- 收到欢迎邮件后即可使用

---

## 步骤2: 访问SES控制台 🖥️

### 2.1 登录AWS管理控制台
1. 访问 [https://console.aws.amazon.com/](https://console.aws.amazon.com/)
2. 输入邮箱和密码登录

### 2.2 选择AWS区域
```
推荐区域:
🇺🇸 us-east-1 (弗吉尼亚北部) - 最便宜，功能最全
🇺🇸 us-west-2 (俄勒冈) - 西海岸用户
🇪🇺 eu-west-1 (爱尔兰) - 欧洲用户
🇦🇺 ap-southeast-2 (悉尼) - 亚太用户
```

### 2.3 打开SES服务
1. 在控制台顶部搜索框输入 **"SES"**
2. 点击 **"Simple Email Service"**
3. 或直接访问: [https://console.aws.amazon.com/ses/](https://console.aws.amazon.com/ses/)

---

## 步骤3: 验证发送方身份 ✅

### 3.1 验证邮箱地址（适合测试）

#### 操作步骤:
1. 在SES控制台左侧菜单，点击 **"Verified identities"**
2. 点击橙色按钮 **"Create identity"**
3. 选择 **"Email address"**
4. 输入你的邮箱地址（如: `<EMAIL>`）
5. 点击 **"Create identity"**

#### 验证邮箱:
1. 查收邮箱中的验证邮件
2. 邮件主题: "Amazon Web Services – Email Address Verification Request"
3. 点击邮件中的验证链接
4. 看到确认页面即验证成功

### 3.2 验证域名（推荐生产环境）

#### 前提条件:
- 拥有自己的域名（如: `yourdomain.com`）
- 能够修改域名的DNS记录

#### 操作步骤:
1. 在SES控制台，点击 **"Create identity"**
2. 选择 **"Domain"**
3. 输入域名: `yourdomain.com`
4. 勾选 **"Use a default DKIM signing key pair"**
5. 点击 **"Create identity"**

#### 配置DNS记录:
1. SES会显示需要添加的DNS记录
2. 复制显示的TXT记录
3. 登录你的域名DNS管理面板（如Cloudflare、阿里云等）
4. 添加TXT记录:
   ```
   类型: TXT
   名称: _amazonses.yourdomain.com
   值: (SES提供的长字符串)
   TTL: 300
   ```
5. 等待DNS传播（通常5-30分钟）
6. 返回SES控制台，状态变为 "Verified" 即成功

---

## 步骤4: 申请移出沙盒模式 🚀

### 4.1 了解沙盒模式
**沙盒限制:**
- 只能发送到已验证的邮箱地址
- 每天最多200封邮件
- 每秒最多1封邮件

**生产模式:**
- 可以发送到任何邮箱地址
- 更高的发送限额
- 更快的发送速率

### 4.2 提交申请

#### 操作步骤:
1. 在SES控制台，点击左侧 **"Account dashboard"**
2. 查看 "Sending quota" 部分
3. 点击 **"Request production access"**

#### 填写申请表单:
```
📧 Mail type: Transactional (事务邮件)
🌐 Website URL: https://yourdomain.com (你的网站)
📝 Use case description: (详细描述，英文)
```

#### 用例描述示例:
```
We are developing a web application for cognitive health analysis. 
The application allows users to register accounts and upload audio files 
for AI-powered analysis. We need to send transactional emails including:

1. Account registration confirmation emails
2. Password reset emails  
3. Analysis completion notifications
4. Important system notifications

We expect to send approximately 1,000-5,000 emails per month initially, 
growing to 10,000-20,000 emails per month as our user base expands. 
All emails will be sent to users who have explicitly registered for our service.

We have implemented proper bounce and complaint handling, and will monitor 
our sending reputation closely. We understand AWS SES policies and will 
comply with all anti-spam regulations.
```

#### 其他信息:
```
📧 Additional contact addresses: (可选)
🗣️ Preferred contact language: English
```

### 4.3 等待审核
- **审核时间**: 通常24-48小时
- **审核结果**: 通过邮件通知
- **如果被拒**: 可以修改申请重新提交

---

## 步骤5: 创建IAM用户和访问密钥 🔑

### 5.1 访问IAM控制台
1. 在AWS控制台搜索 **"IAM"**
2. 点击 **"Identity and Access Management (IAM)"**

### 5.2 创建IAM用户
1. 点击左侧菜单 **"Users"**
2. 点击 **"Add user"** 或 **"Create user"**
3. 输入用户名: `ses-django-user`
4. 选择访问类型: **"Programmatic access"**
5. 点击 **"Next: Permissions"**

### 5.3 设置权限
1. 选择 **"Attach existing policies directly"**
2. 在搜索框输入: `SES`
3. 勾选 **"AmazonSESFullAccess"**
4. 点击 **"Next: Tags"** (可跳过)
5. 点击 **"Next: Review"**
6. 点击 **"Create user"**

### 5.4 保存访问密钥
```
⚠️ 重要: 这是唯一一次可以查看Secret Access Key的机会!

📋 Access Key ID: AKIA... (复制保存)
🔐 Secret Access Key: ... (复制保存)
```

**保存方式:**
- 点击 **"Download .csv"** 下载文件
- 或手动复制到安全的地方
- **不要**分享给任何人

---

## 步骤6: 配置Django项目 ⚙️

### 6.1 安装依赖
```bash
cd backend
pip install django-ses boto3 python-dotenv
```

### 6.2 创建环境变量文件
```bash
# 创建 .env 文件
cp .env.example .env
```

### 6.3 配置环境变量
编辑 `.env` 文件:
```bash
# 邮件服务配置
EMAIL_SERVICE=ses

# AWS SES 配置
AWS_ACCESS_KEY_ID=AKIA...  # 你的Access Key ID
AWS_SECRET_ACCESS_KEY=...  # 你的Secret Access Key
AWS_SES_REGION_NAME=us-east-1  # 你选择的区域

# 发送方邮箱 (必须是已验证的)
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 6.4 验证Django配置
检查 `backend/core/settings.py` 是否包含:
```python
# Email settings
EMAIL_SERVICE = os.environ.get('EMAIL_SERVICE', 'console')

if EMAIL_SERVICE == 'ses':
    EMAIL_BACKEND = 'django_ses.SESBackend'
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_SES_REGION_NAME = os.environ.get('AWS_SES_REGION_NAME', 'us-east-1')
    AWS_SES_REGION_ENDPOINT = f'email.{AWS_SES_REGION_NAME}.amazonaws.com'
```

---

## 步骤7: 测试邮件发送 🧪

### 7.1 运行测试脚本
```bash
cd backend
python test_amazon_ses.py
```

### 7.2 手动测试
```bash
# 启动Django shell
python manage.py shell

# 测试发送
from django.core.mail import send_mail

result = send_mail(
    subject='SES测试邮件',
    message='这是一封测试邮件',
    from_email='<EMAIL>',  # 已验证的发送方
    recipient_list=['<EMAIL>'],  # 你的邮箱
    fail_silently=False,
)

print(f"发送结果: {result}")  # 1表示成功
```

### 7.3 检查发送统计
```bash
# 在Django shell中
import boto3

ses = boto3.client(
    'ses',
    aws_access_key_id='your-access-key',
    aws_secret_access_key='your-secret-key',
    region_name='us-east-1'
)

# 查看配额
quota = ses.get_send_quota()
print(f"24小时限制: {quota['Max24HourSend']}")
print(f"已发送: {quota['SentLast24Hours']}")
```

---

## 🚨 常见问题和故障排除

### 问题1: 邮件发送失败 - "MessageRejected"

#### 可能原因:
- 发送方邮箱未验证
- 邮件内容包含垃圾邮件特征
- 收件人邮箱格式错误

#### 解决方案:
```python
# 1. 检查发送方是否已验证
from django.conf import settings
print(f"发送方: {settings.DEFAULT_FROM_EMAIL}")

# 2. 在SES控制台检查 "Verified identities"
# 3. 确保邮件内容简洁，避免垃圾邮件词汇
```

### 问题2: "AccessDenied" 错误

#### 可能原因:
- AWS凭证错误
- IAM权限不足
- 区域设置错误

#### 解决方案:
```python
# 检查凭证配置
import boto3
try:
    ses = boto3.client('ses', region_name='us-east-1')
    quota = ses.get_send_quota()
    print("✅ 凭证正确")
except Exception as e:
    print(f"❌ 凭证错误: {e}")
```

### 问题3: 仍在沙盒模式

#### 症状:
- 只能发送到已验证邮箱
- 每天限制200封邮件

#### 解决方案:
1. 检查申请状态: SES控制台 → Account dashboard
2. 如果被拒绝，修改申请重新提交
3. 联系AWS支持

### 问题4: DNS验证失败

#### 解决方案:
```bash
# 检查DNS记录是否正确添加
nslookup -type=TXT _amazonses.yourdomain.com

# 或使用在线工具检查DNS传播
# https://dnschecker.org/
```

---

## 🔧 高级配置

### 配置1: 启用DKIM签名

#### 操作步骤:
1. 在SES控制台，选择已验证的域名
2. 点击 **"DKIM"** 标签
3. 点击 **"Edit"**
4. 启用 **"Easy DKIM"**
5. 添加显示的CNAME记录到DNS

#### DNS记录示例:
```
类型: CNAME
名称: abc123._domainkey.yourdomain.com
值: abc123.dkim.amazonses.com
```

### 配置2: 设置Configuration Set

#### 创建Configuration Set:
1. SES控制台 → **"Configuration sets"**
2. 点击 **"Create set"**
3. 输入名称: `my-app-config-set`
4. 添加事件目的地（可选）

#### 在Django中使用:
```python
# settings.py
AWS_SES_CONFIGURATION_SET = 'my-app-config-set'

# 或在发送时指定
from django.core.mail import EmailMessage
msg = EmailMessage(
    subject='测试',
    body='内容',
    from_email='<EMAIL>',
    to=['<EMAIL>'],
    headers={'X-SES-CONFIGURATION-SET': 'my-app-config-set'}
)
msg.send()
```

### 配置3: 处理退信和投诉

#### 设置SNS通知:
1. 创建SNS主题
2. 在Configuration Set中添加事件目的地
3. 选择事件类型: Bounce, Complaint, Delivery

#### Django处理示例:
```python
# views.py
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
import json

@csrf_exempt
def handle_ses_notification(request):
    if request.method == 'POST':
        message = json.loads(request.body)

        if message['Type'] == 'Notification':
            sns_message = json.loads(message['Message'])

            if sns_message['notificationType'] == 'Bounce':
                # 处理退信
                bounced_recipients = sns_message['bounce']['bouncedRecipients']
                for recipient in bounced_recipients:
                    email = recipient['emailAddress']
                    # 将邮箱加入黑名单
                    print(f"邮箱退信: {email}")

            elif sns_message['notificationType'] == 'Complaint':
                # 处理投诉
                complained_recipients = sns_message['complaint']['complainedRecipients']
                for recipient in complained_recipients:
                    email = recipient['emailAddress']
                    # 将邮箱加入黑名单
                    print(f"邮箱投诉: {email}")

    return HttpResponse('OK')
```

---

## 📊 监控和优化

### 监控发送统计
```python
import boto3
from datetime import datetime, timedelta

ses = boto3.client('ses', region_name='us-east-1')

# 获取最近24小时统计
stats = ses.get_send_statistics()
for stat in stats['SendDataPoints'][-24:]:
    print(f"时间: {stat['Timestamp']}")
    print(f"发送: {stat['DeliveryAttempts']}")
    print(f"退信: {stat['Bounces']}")
    print(f"投诉: {stat['Complaints']}")
    print("---")
```

### 成本监控
```python
# 计算月度成本
monthly_emails = 50000  # 每月邮件数
free_tier = 62000 if using_ec2 else 2000
billable_emails = max(0, monthly_emails - free_tier)
monthly_cost = billable_emails * 0.0001
print(f"预计月度成本: ${monthly_cost:.2f}")
```

### 性能优化
```python
# settings.py
# 控制发送速率，避免限流
AWS_SES_AUTO_THROTTLE = 0.5  # 每秒0.5封

# 启用连接池
AWS_SES_CONNECTION_POOL_SIZE = 10
```

---

## ✅ 配置完成检查清单

### 基础配置 ✓
- [ ] AWS账户已创建并激活
- [ ] SES服务已访问
- [ ] 发送方身份已验证（邮箱或域名）
- [ ] 已申请移出沙盒模式（生产环境）
- [ ] IAM用户已创建，权限已设置
- [ ] 访问密钥已保存

### Django配置 ✓
- [ ] 依赖包已安装 (`django-ses`, `boto3`)
- [ ] 环境变量已配置
- [ ] Django settings已更新
- [ ] 测试邮件发送成功

### 高级配置 ✓
- [ ] DKIM签名已启用（推荐）
- [ ] Configuration Set已创建（可选）
- [ ] 退信/投诉处理已设置（生产环境推荐）
- [ ] 监控和统计已配置

### 安全检查 ✓
- [ ] 访问密钥未泄露
- [ ] 环境变量未提交到代码库
- [ ] IAM权限最小化
- [ ] 定期轮换访问密钥

---

## 🎉 恭喜！

如果你完成了以上所有步骤，你的Amazon SES已经完全配置好了！

### 下一步:
1. 开始发送真实邮件
2. 监控发送统计和成本
3. 根据需要调整配置
4. 享受超低成本的邮件服务！

### 技术支持:
- AWS SES文档: https://docs.aws.amazon.com/ses/
- AWS支持中心: https://console.aws.amazon.com/support/
- 社区论坛: https://forums.aws.amazon.com/
```
