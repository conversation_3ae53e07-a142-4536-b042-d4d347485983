#!/usr/bin/env python
"""
Amazon SES 测试脚本
用于测试Amazon SES邮件发送功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import boto3
from django.core.mail import send_mail, EmailMultiAlternatives
from django.conf import settings
from botocore.exceptions import ClientError


def test_aws_credentials():
    """测试AWS凭证配置"""
    print("🔑 测试AWS凭证...")
    print("=" * 50)
    
    access_key = getattr(settings, 'AWS_ACCESS_KEY_ID', None)
    secret_key = getattr(settings, 'AWS_SECRET_ACCESS_KEY', None)
    region = getattr(settings, 'AWS_SES_REGION_NAME', 'us-east-1')
    
    print(f"AWS_ACCESS_KEY_ID: {'已设置' if access_key else '未设置'}")
    print(f"AWS_SECRET_ACCESS_KEY: {'已设置' if secret_key else '未设置'}")
    print(f"AWS_SES_REGION_NAME: {region}")
    
    if not access_key or not secret_key:
        print("❌ AWS凭证未配置，请检查环境变量或settings.py")
        return False
    
    try:
        # 测试连接
        ses_client = boto3.client(
            'ses',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        
        # 获取发送配额
        quota = ses_client.get_send_quota()
        print(f"✅ AWS连接成功")
        print(f"24小时发送限制: {quota['Max24HourSend']}")
        print(f"每秒发送限制: {quota['MaxSendRate']}")
        print(f"已发送数量: {quota['SentLast24Hours']}")
        
        return True
        
    except ClientError as e:
        print(f"❌ AWS连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


def test_verified_identities():
    """测试已验证的身份"""
    print("\n📧 检查已验证的身份...")
    print("=" * 50)
    
    try:
        ses_client = boto3.client(
            'ses',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_SES_REGION_NAME
        )
        
        # 获取已验证的身份
        identities = ses_client.list_verified_email_addresses()
        domains = ses_client.list_identities(IdentityType='Domain')
        
        print("已验证的邮箱地址:")
        if identities['VerifiedEmailAddresses']:
            for email in identities['VerifiedEmailAddresses']:
                print(f"  ✅ {email}")
        else:
            print("  ❌ 没有已验证的邮箱地址")
        
        print("\n已验证的域名:")
        domain_identities = [i for i in domains['Identities'] if '.' in i]
        if domain_identities:
            for domain in domain_identities:
                print(f"  ✅ {domain}")
        else:
            print("  ❌ 没有已验证的域名")
        
        # 检查默认发送方是否已验证
        default_from = settings.DEFAULT_FROM_EMAIL
        print(f"\n默认发送方: {default_from}")
        
        # 检查是否在已验证列表中
        is_verified = False
        if default_from in identities['VerifiedEmailAddresses']:
            is_verified = True
        else:
            # 检查域名是否已验证
            domain = default_from.split('@')[1]
            if domain in domain_identities:
                is_verified = True
        
        if is_verified:
            print("✅ 默认发送方已验证")
        else:
            print("❌ 默认发送方未验证，需要先验证身份")
        
        return is_verified
        
    except Exception as e:
        print(f"❌ 检查验证身份失败: {e}")
        return False


def test_send_simple_email():
    """测试发送简单邮件"""
    print("\n📮 测试发送简单邮件...")
    print("=" * 50)
    
    # 获取测试邮箱
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 未输入测试邮箱，跳过测试")
        return False
    
    try:
        result = send_mail(
            subject='Amazon SES 测试邮件',
            message='这是一封通过Amazon SES发送的测试邮件。\n\n如果您收到此邮件，说明SES配置成功！',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[test_email],
            fail_silently=False,
        )
        
        if result == 1:
            print("✅ 邮件发送成功！")
            print(f"发送方: {settings.DEFAULT_FROM_EMAIL}")
            print(f"接收方: {test_email}")
            return True
        else:
            print("❌ 邮件发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        return False


def test_send_html_email():
    """测试发送HTML邮件"""
    print("\n🎨 测试发送HTML邮件...")
    print("=" * 50)
    
    # 获取测试邮箱
    test_email = input("请输入测试邮箱地址 (回车跳过): ").strip()
    if not test_email:
        print("跳过HTML邮件测试")
        return True
    
    try:
        subject = 'Amazon SES HTML 测试邮件'
        text_content = '这是纯文本版本的邮件内容。'
        html_content = '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Amazon SES 测试</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center; }
                .content { padding: 20px 0; }
                .footer { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; font-size: 14px; }
                .success { color: #28a745; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Amazon SES 测试成功！</h1>
                </div>
                <div class="content">
                    <p>恭喜！您已成功配置Amazon SES邮件发送服务。</p>
                    <p class="success">✅ HTML邮件发送正常</p>
                    <p><strong>服务信息：</strong></p>
                    <ul>
                        <li>邮件服务：Amazon SES</li>
                        <li>发送方：''' + settings.DEFAULT_FROM_EMAIL + '''</li>
                        <li>区域：''' + getattr(settings, 'AWS_SES_REGION_NAME', 'us-east-1') + '''</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>此邮件由Django应用通过Amazon SES发送</p>
                </div>
            </div>
        </body>
        </html>
        '''
        
        msg = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[test_email]
        )
        msg.attach_alternative(html_content, "text/html")
        
        result = msg.send()
        
        if result == 1:
            print("✅ HTML邮件发送成功！")
            return True
        else:
            print("❌ HTML邮件发送失败")
            return False
            
    except Exception as e:
        print(f"❌ HTML邮件发送失败: {e}")
        return False


def test_send_statistics():
    """查看发送统计"""
    print("\n📊 查看发送统计...")
    print("=" * 50)
    
    try:
        ses_client = boto3.client(
            'ses',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_SES_REGION_NAME
        )
        
        # 获取发送统计
        stats = ses_client.get_send_statistics()
        
        if stats['SendDataPoints']:
            print("最近发送统计:")
            for i, stat in enumerate(stats['SendDataPoints'][-5:]):  # 显示最近5个数据点
                print(f"  {i+1}. 时间: {stat['Timestamp']}")
                print(f"     发送数: {stat['DeliveryAttempts']}")
                print(f"     退信数: {stat['Bounces']}")
                print(f"     投诉数: {stat['Complaints']}")
                print(f"     拒绝数: {stat['Rejects']}")
                print()
        else:
            print("暂无发送统计数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取统计失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Amazon SES 测试开始")
    print("=" * 60)
    
    # 检查邮件后端配置
    email_backend = getattr(settings, 'EMAIL_BACKEND', '')
    print(f"当前邮件后端: {email_backend}")
    
    if 'ses' not in email_backend.lower():
        print("⚠️  当前未使用SES后端，请检查EMAIL_SERVICE环境变量")
        print("   设置 EMAIL_SERVICE=ses 来启用Amazon SES")
        return
    
    # 执行测试
    tests = [
        ("AWS凭证测试", test_aws_credentials),
        ("验证身份检查", test_verified_identities),
        ("简单邮件发送", test_send_simple_email),
        ("HTML邮件发送", test_send_html_email),
        ("发送统计查看", test_send_statistics),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except KeyboardInterrupt:
            print("\n\n⏹️  测试被用户中断")
            break
        except Exception as e:
            print(f"\n❌ {test_name}出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n总计: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！Amazon SES配置成功！")
    else:
        print("⚠️  部分测试失败，请检查配置")


if __name__ == "__main__":
    main()
