# 📧 邮件发送配置说明

## 当前邮件发送实现

### 1. 邮件发送流程
1. 用户注册时调用 `UserRegistrationSerializer.create()`
2. 创建用户后调用 `send_verification_email(user.email)`
3. 生成随机令牌并存储到 `EmailVerificationToken` 表
4. 发送包含激活链接的HTML邮件

### 2. 激活链接格式
```
http://192.168.50.180:8001/api/verify-email/?token=<随机令牌>
```

### 3. 邮件内容
- **主题**：🧠 激活您的认知健康账户
- **发送方**：<EMAIL>
- **格式**：HTML + 纯文本
- **有效期**：24小时

## 当前配置状态

### 开发环境 (DEBUG=True)
- **EMAIL_BACKEND**: `django.core.mail.backends.console.EmailBackend`
- **效果**：邮件内容打印到后端控制台，不会真正发送
- **优点**：无需配置SMTP，便于开发调试
- **缺点**：用户收不到真实邮件

### 生产环境 (DEBUG=False)
- **EMAIL_BACKEND**: `django.core.mail.backends.smtp.EmailBackend`
- **SMTP服务器**：Gmail (smtp.gmail.com:587)
- **需要配置**：EMAIL_HOST_USER 和 EMAIL_HOST_PASSWORD

## 🔧 如何配置真实邮件发送

### 方案1：使用Gmail SMTP (推荐)

1. **获取Gmail应用密码**：
   - 登录Gmail账户
   - 启用两步验证
   - 生成应用专用密码

2. **修改后端配置** (`backend/core/settings.py`)：
```python
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'  # 16位应用密码
```

### 方案2：使用其他SMTP服务

#### 🌍 国际邮箱服务

**Outlook/Hotmail (Microsoft)**：
```python
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # 或 @hotmail.com
EMAIL_HOST_PASSWORD = 'your-password'
```

**Yahoo Mail**：
```python
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'  # 需要应用密码
```

**Apple iCloud Mail**：
```python
EMAIL_HOST = 'smtp.mail.me.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # 或 @me.com
EMAIL_HOST_PASSWORD = 'your-app-password'  # 需要应用密码
```

**Zoho Mail**：
```python
EMAIL_HOST = 'smtp.zoho.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**ProtonMail**：
```python
EMAIL_HOST = 'smtp.protonmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-bridge-password'  # 需要ProtonMail Bridge
```

#### 🇨🇳 中国邮箱服务

**QQ邮箱 (腾讯)**：
```python
EMAIL_HOST = 'smtp.qq.com'
EMAIL_PORT = 587  # 或 465 (SSL)
EMAIL_USE_TLS = True  # 或 EMAIL_USE_SSL = True (端口465)
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-authorization-code'  # 授权码，非QQ密码
```

**163邮箱 (网易)**：
```python
EMAIL_HOST = 'smtp.163.com'
EMAIL_PORT = 25  # 或 465 (SSL), 994 (SSL)
EMAIL_USE_TLS = False
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-authorization-code'  # 授权码
```

**126邮箱 (网易)**：
```python
EMAIL_HOST = 'smtp.126.com'
EMAIL_PORT = 25  # 或 465 (SSL)
EMAIL_USE_TLS = False
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-authorization-code'  # 授权码
```

**新浪邮箱**：
```python
EMAIL_HOST = 'smtp.sina.com'
EMAIL_PORT = 25  # 或 465 (SSL)
EMAIL_USE_TLS = False
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**搜狐邮箱**：
```python
EMAIL_HOST = 'smtp.sohu.com'
EMAIL_PORT = 25
EMAIL_USE_TLS = False
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**阿里云邮箱**：
```python
EMAIL_HOST = 'smtp.mxhichina.com'  # 企业邮箱
EMAIL_PORT = 465
EMAIL_USE_SSL = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

#### 📧 专业邮件服务 (推荐用于生产环境)

**SendGrid**：
```python
EMAIL_BACKEND = 'sendgrid_backend.SendgridBackend'
SENDGRID_API_KEY = 'your-sendgrid-api-key'
# 需要安装: pip install sendgrid-django
```

**Mailgun**：
```python
EMAIL_BACKEND = 'anymail.backends.mailgun.EmailBackend'
ANYMAIL = {
    'MAILGUN_API_KEY': 'your-mailgun-api-key',
    'MAILGUN_SENDER_DOMAIN': 'your-domain.com',
}
# 需要安装: pip install django-anymail
```

**Amazon SES**：
```python
EMAIL_BACKEND = 'django_ses.SESBackend'
AWS_ACCESS_KEY_ID = 'your-access-key'
AWS_SECRET_ACCESS_KEY = 'your-secret-key'
AWS_SES_REGION_NAME = 'us-east-1'
AWS_SES_REGION_ENDPOINT = 'email.us-east-1.amazonaws.com'
# 需要安装: pip install django-ses
```

**Mailjet**：
```python
EMAIL_BACKEND = 'anymail.backends.mailjet.EmailBackend'
ANYMAIL = {
    'MAILJET_API_KEY': 'your-api-key',
    'MAILJET_SECRET_KEY': 'your-secret-key',
}
```

**Postmark**：
```python
EMAIL_BACKEND = 'anymail.backends.postmark.EmailBackend'
ANYMAIL = {
    'POSTMARK_SERVER_TOKEN': 'your-server-token',
}
```

#### 🌐 其他国际邮箱服务

**GMX Mail (德国)**：
```python
EMAIL_HOST = 'smtp.gmx.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**Mail.com**：
```python
EMAIL_HOST = 'smtp.mail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**Yandex Mail (俄罗斯)**：
```python
EMAIL_HOST = 'smtp.yandex.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

**FastMail**：
```python
EMAIL_HOST = 'smtp.fastmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

#### 🏢 企业邮箱服务

**腾讯企业邮箱**：
```python
EMAIL_HOST = 'smtp.exmail.qq.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**网易企业邮箱**：
```python
EMAIL_HOST = 'smtp.ym.163.com'
EMAIL_PORT = 994
EMAIL_USE_SSL = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

**Google Workspace (G Suite)**：
```python
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

**Microsoft 365**：
```python
EMAIL_HOST = 'smtp.office365.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
```

## 📊 邮箱服务选择建议

### 🏆 **推荐排序 (个人开发)**

1. **Gmail** - 最稳定，配置简单，全球可达性好
2. **Outlook/Hotmail** - Microsoft服务，稳定性好
3. **QQ邮箱** - 国内用户多，配置简单
4. **163/126邮箱** - 国内老牌邮箱，稳定

### 🏢 **推荐排序 (生产环境)**

1. **SendGrid** - 专业邮件服务，送达率高，有免费额度
2. **Mailgun** - 开发者友好，API丰富
3. **Amazon SES** - AWS生态，价格便宜，可扩展性强
4. **腾讯企业邮箱** - 国内企业首选
5. **阿里云邮箱** - 阿里云生态

### 🔍 **选择标准**

| 标准 | Gmail | QQ邮箱 | SendGrid | Amazon SES |
|------|-------|--------|----------|------------|
| 配置难度 | 简单 | 简单 | 中等 | 中等 |
| 送达率 | 高 | 中等 | 很高 | 高 |
| 免费额度 | 有限 | 有限 | 100封/天 | 200封/天 |
| 国内访问 | 不稳定 | 稳定 | 稳定 | 稳定 |
| 企业级功能 | 基础 | 基础 | 丰富 | 丰富 |

### 方案3：临时禁用邮件验证

如果暂时不需要邮件验证，可以：

1. **自动激活用户** (已实现)：
```python
validated_data['is_active'] = True
```

2. **注释邮件发送**：
```python
# send_verification_email(user.email)
```

## 🐛 故障排除

### 问题1：邮件发送成功但收不到
- **原因**：可能在垃圾邮件文件夹
- **解决**：检查垃圾邮件，添加发送方到白名单

### 问题2：SMTP认证失败
- **原因**：用户名/密码错误，或未启用应用密码
- **解决**：检查凭据，确保使用应用专用密码

### 问题3：连接超时
- **原因**：防火墙阻止SMTP端口
- **解决**：检查网络设置，尝试不同端口

## 📝 测试邮件发送

### 在Django Shell中测试：
```python
python manage.py shell

from django.core.mail import send_mail
from django.conf import settings

send_mail(
    '测试邮件',
    '这是一封测试邮件',
    settings.DEFAULT_FROM_EMAIL,
    ['<EMAIL>'],
    fail_silently=False,
)
```

### 检查邮件配置：
```python
from django.conf import settings
print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
print(f"EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', '未设置')}")
print(f"EMAIL_HOST_USER: {getattr(settings, 'EMAIL_HOST_USER', '未设置')}")
```

## 🔍 调试信息

当前邮件发送会在后端控制台输出：
- ✅ 验证邮件已发送到 {email}
- ❌ 发送验证邮件失败 {email}: {错误信息}

检查后端控制台日志可以了解邮件发送状态。
